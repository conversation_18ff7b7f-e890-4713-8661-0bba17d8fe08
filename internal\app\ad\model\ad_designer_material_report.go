// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/model/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdDesignerMaterialReportInfoRes is the golang structure for table ad_designer_material_report.
type AdDesignerMaterialReportInfoRes struct {
	gmeta.Meta           `orm:"table:ad_designer_material_report"`
	StatDate             *gtime.Time `orm:"stat_date,primary" json:"statDate" dc:"统计日期"`                       // 统计日期
	DesignerName         string      `orm:"designer_name,primary" json:"designerName" dc:"设计师名称"`              // 设计师名称
	DesignerId           int         `orm:"designer_id" json:"designerId" dc:"设计师名称"`                          // 设计师名称
	NewMaterialCount     int         `orm:"new_material_count" json:"newMaterialCount" dc:"上新素材数"`             // 上新素材数
	ExposedMaterialCount int         `orm:"exposed_material_count" json:"exposedMaterialCount" dc:"曝光大于0的素材数"` // 曝光大于0的素材数
	NewMaterialCost      float64     `orm:"new_material_cost" json:"newMaterialCost" dc:"上新素材消耗"`              // 上新素材消耗
	TotalCost            float64     `orm:"total_cost" json:"totalCost" dc:"总消耗"`                              // 总消耗
	Impressions          int64       `orm:"impressions" json:"impressions" dc:"展示数"`                           // 展示数
	AvgCpm               float64     `orm:"avg_cpm" json:"avgCpm" dc:"平均千次展现成本"`                               // 平均千次展现成本
	Clicks               int64       `orm:"clicks" json:"clicks" dc:"点击数"`                                     // 点击数
	Ctr                  float64     `orm:"ctr" json:"ctr" dc:"点击率"`                                           // 点击率
	Conversions          int64       `orm:"conversions" json:"conversions" dc:"转化数"`                           // 转化数
	Cpa                  float64     `orm:"cpa" json:"cpa" dc:"转化成本"`                                          // 转化成本
	ConversionRate       float64     `orm:"conversion_rate" json:"conversionRate" dc:"转化率"`                    // 转化率
	Actives              int64       `orm:"actives" json:"actives" dc:"激活数"`                                   // 激活数
	ActiveCost           float64     `orm:"active_cost" json:"activeCost" dc:"激活成本"`                           // 激活成本
	ActiveRate           float64     `orm:"active_rate" json:"activeRate" dc:"激活率"`                            // 激活率
	FirstPayCount        int64       `orm:"first_pay_count" json:"firstPayCount" dc:"首次付费数"`                   // 首次付费数
	FirstPayRate         float64     `orm:"first_pay_rate" json:"firstPayRate" dc:"首次付费率"`                     // 首次付费率
}

type AdDesignerMaterialReportListRes struct {
	StatDate             string  `json:"statDate" dc:"统计日期"`
	DesignerName         string  `json:"designerName" dc:"设计师名称"`
	DesignerId           int     `json:"designerId" dc:"设计师Id"`
	DesignerManager      string  `json:"designerManager" dc:"设计师主管"`
	NewMaterialCount     int     `json:"newMaterialCount" dc:"上新素材数"`
	ExposedMaterialCount int     `json:"exposedMaterialCount" dc:"曝光大于0的素材数"`
	NewMaterialCost      float64 `json:"newMaterialCost" dc:"上新素材消耗"`
	TotalCost            float64 `json:"totalCost" dc:"总消耗"`
	Impressions          int64   `json:"impressions" dc:"展示数"`
	AvgCpm               float64 `json:"avgCpm" dc:"平均千次展现成本"`
	Clicks               int64   `json:"clicks" dc:"点击数"`
	Ctr                  float64 `json:"ctr" dc:"点击率"`
	Conversions          int64   `json:"conversions" dc:"转化数"`
	Cpa                  float64 `json:"cpa" dc:"转化成本"`
	ConversionRate       float64 `json:"conversionRate" dc:"转化率"`
	Actives              int64   `json:"actives" dc:"激活数"`
	ActiveCost           float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate           float64 `json:"activeRate" dc:"激活率"`
	FirstPayCount        int64   `json:"firstPayCount" dc:"首次付费数"`
	FirstPayRate         float64 `json:"firstPayRate" dc:"首次付费率"`
}

type AdDesignerMaterialReportReq struct {
	comModel.PageReq
	DeptIds   []int  `p:"deptIds"`
	StartTime string `p:"startTime" dc:"下单时间"`
	EndTime   string `p:"endTime" dc:"结束下单时间 YYYY-MM-DD格式"`
	// 设计师主管 模糊内容搜索
	DesignerManager string `p:"designerManager"`
	// 设计师 模糊内容搜索
	//Designer string `p:"designer"`
}

type AdDesignerMaterialReportRes struct {
	comModel.ListRes
	List    []*AdDesignerMaterialReportListRes `json:"list"`
	Summary *AdDesignerMaterialSummary         `json:"summary"`
}

// AdDesignerMaterialReportSearchReq 分页请求参数
type AdDesignerMaterialReportSearchReq struct {
	comModel.PageReq
	DeptIds   []int  `p:"deptIds"`
	StartTime string `p:"startTime" dc:"下单时间"`
	EndTime   string `p:"endTime" dc:"结束下单时间 YYYY-MM-DD格式"`
	// 设计师主管 模糊内容搜索
	DesignerManager string `p:"designerManager"`
	// 设计师 模糊内容搜索
	Designer string `p:"designer"`
}

// AdDesignerMaterialReportSearchRes 列表返回结果
type AdDesignerMaterialSearchRes struct {
	comModel.ListRes
	List    []*AdDesignerMaterialReportListRes `json:"list"`
	Summary *AdDesignerMaterialSummary         `json:"summary"`
}

type AdDesignerMaterialSummary struct {
	NewMaterialCount     int     `json:"newMaterialCount"  dc:"上新素材数"`
	ExposedMaterialCount int     `json:"exposedMaterialCount"  dc:"曝光大于0的素材数"`
	NewMaterialCost      float64 `json:"newMaterialCost"  dc:"上新素材消耗"`
	TotalCost            float64 `json:"totalCost"  dc:"总消耗"`
	Impressions          int64   `json:"impressions"  dc:"展示数"`
	AvgCpm               float64 `json:"avgCpm"  dc:"平均千次展现成本"`
	Clicks               int64   `json:"clicks"  dc:"点击数"`
	Ctr                  float64 `json:"ctr"  dc:"点击率"`
	Conversions          int64   `json:"conversions"  dc:"转化数"`
	Cpa                  float64 `json:"cpa"  dc:"转化成本"`
	ConversionRate       float64 `json:"conversionRate"  dc:"转化率"`
	Actives              int64   `json:"actives"  dc:"激活数"`
	ActiveCost           float64 `json:"activeCost"  dc:"激活成本"`
	ActiveRate           float64 `json:"activeRate"  dc:"激活率"`
	FirstPayCount        int64   `json:"firstPayCount"  dc:"首次付费数"`
	FirstPayRate         float64 `json:"firstPayRate"  dc:"首次付费率"`
}

// AdDesignerMaterialReportAddReq 添加操作请求参数
type AdDesignerMaterialReportAddReq struct {
	StatDate             string  `p:"statDate" v:"required#主键ID不能为空" dc:"统计日期"`
	DesignerId           int     `p:"designerId" v:"required#设计师名称不能为空" dc:"设计师名称"`
	DesignerName         string  `p:"designerName" dc:"设计师名称"` // 设计师名称
	NewMaterialCount     int     `p:"newMaterialCount"  dc:"上新素材数"`
	ExposedMaterialCount int     `p:"exposedMaterialCount"  dc:"曝光大于0的素材数"`
	NewMaterialCost      float64 `p:"newMaterialCost"  dc:"上新素材消耗"`
	TotalCost            float64 `p:"totalCost"  dc:"总消耗"`
	Impressions          int64   `p:"impressions"  dc:"展示数"`
	AvgCpm               float64 `p:"avgCpm"  dc:"平均千次展现成本"`
	Clicks               int64   `p:"clicks"  dc:"点击数"`
	Ctr                  float64 `p:"ctr"  dc:"点击率"`
	Conversions          int64   `p:"conversions"  dc:"转化数"`
	Cpa                  float64 `p:"cpa"  dc:"转化成本"`
	ConversionRate       float64 `p:"conversionRate"  dc:"转化率"`
	Actives              int64   `p:"actives"  dc:"激活数"`
	ActiveCost           float64 `p:"activeCost"  dc:"激活成本"`
	ActiveRate           float64 `p:"activeRate"  dc:"激活率"`
	FirstPayCount        int64   `p:"firstPayCount"  dc:"首次付费数"`
	FirstPayRate         float64 `p:"firstPayRate"  dc:"首次付费率"`
}

// AdDesignerMaterialReportEditReq 修改操作请求参数
type AdDesignerMaterialReportEditReq struct {
	StatDate             string  `p:"statDate" v:"required#主键ID不能为空" dc:"统计日期"`
	DesignerId           int     `p:"designerId" v:"required#设计师名称不能为空" dc:"设计师名称"`
	NewMaterialCount     int     `p:"newMaterialCount"  dc:"上新素材数"`
	ExposedMaterialCount int     `p:"exposedMaterialCount"  dc:"曝光大于0的素材数"`
	NewMaterialCost      float64 `p:"newMaterialCost"  dc:"上新素材消耗"`
	TotalCost            float64 `p:"totalCost"  dc:"总消耗"`
	Impressions          int64   `p:"impressions"  dc:"展示数"`
	AvgCpm               float64 `p:"avgCpm"  dc:"平均千次展现成本"`
	Clicks               int64   `p:"clicks"  dc:"点击数"`
	Ctr                  float64 `p:"ctr"  dc:"点击率"`
	Conversions          int64   `p:"conversions"  dc:"转化数"`
	Cpa                  float64 `p:"cpa"  dc:"转化成本"`
	ConversionRate       float64 `p:"conversionRate"  dc:"转化率"`
	Actives              int64   `p:"actives"  dc:"激活数"`
	ActiveCost           float64 `p:"activeCost"  dc:"激活成本"`
	ActiveRate           float64 `p:"activeRate"  dc:"激活率"`
	FirstPayCount        int64   `p:"firstPayCount"  dc:"首次付费数"`
	FirstPayRate         float64 `p:"firstPayRate"  dc:"首次付费率"`
}
