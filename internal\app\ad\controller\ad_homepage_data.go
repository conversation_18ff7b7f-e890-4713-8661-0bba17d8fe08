// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-01-24 10:42:39
// 生成路径: internal/app/ad/controller/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adHomepageDataController struct {
	systemController.BaseController
}

var AdHomepageData = new(adHomepageDataController)

// CardData 列表
func (c *adHomepageDataController) CardData(ctx context.Context, req *ad.AdHomepageCardDataReq) (res *ad.AdHomepageCardDataRes, err error) {
	res = new(ad.AdHomepageCardDataRes)
	res.AdHomepageCardDataRes, err = service.AdHomepageData().CardData(ctx, &req.AdHomepageCardDataReq)
	return
}

// OptimizerPieChartData 优化师饼状图数据
func (c *adHomepageDataController) OptimizerPieChartData(ctx context.Context, req *ad.AdHomepageOptimizerPieChartDataReq) (res *ad.AdHomepageOptimizerPieChartDataRes, err error) {
	res = new(ad.AdHomepageOptimizerPieChartDataRes)
	res.AdHomepageOptimizerPieChartDataRes, err = service.AdHomepageData().OptimizerPieChartData(ctx, &req.AdHomepageOptimizerPieChartDataReq)
	return
}

// DesignerPieChartData 设计师饼状图数据
func (c *adHomepageDataController) DesignerPieChartData(ctx context.Context, req *ad.AdHomepageDesignerPieChartDataReq) (res *ad.AdHomepageDesignerPieChartDataRes, err error) {
	res = new(ad.AdHomepageDesignerPieChartDataRes)
	res.AdHomepageDesignerPieChartDataRes, err = service.AdHomepageData().DesignerPieChartData(ctx, &req.AdHomepageDesignerPieChartDataReq)
	return
}

// LineChartData 折线图数据
func (c *adHomepageDataController) LineChartData(ctx context.Context, req *ad.AdHomepageLineChartDataReq) (res *ad.AdHomepageLineChartDataRes, err error) {
	res = new(ad.AdHomepageLineChartDataRes)
	res.AdHomepageLineChartDataRes, err = service.AdHomepageData().LineChartData(ctx, &req.AdHomepageLineChartDataReq)
	return
}
