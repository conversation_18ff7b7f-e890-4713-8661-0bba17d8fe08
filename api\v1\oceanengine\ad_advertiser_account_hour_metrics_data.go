// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: api/v1/oceanengine/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAdvertiserAccountHourMetricsDataSearchReq 分页请求参数
type AdAdvertiserAccountHourMetricsDataSearchReq struct {
	g.Meta `path:"/list" tags:"广告账户小时指标数据" method:"post" summary:"广告账户小时指标数据列表"`
	commonApi.Author
	model.AdAdvertiserAccountHourMetricsDataSearchReq
}

// AdAdvertiserAccountHourMetricsDataSearchRes 列表返回结果
type AdAdvertiserAccountHourMetricsDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountHourMetricsDataSearchRes
}

// AdAdvertiserAccountHourMetricsDataAddReq 添加操作请求参数
type AdAdvertiserAccountHourMetricsDataAddReq struct {
	g.Meta `path:"/add" tags:"广告账户小时指标数据" method:"post" summary:"广告账户小时指标数据添加"`
	commonApi.Author
	*model.AdAdvertiserAccountHourMetricsDataAddReq
}

// AdAdvertiserAccountHourMetricsDataAddRes 添加操作返回结果
type AdAdvertiserAccountHourMetricsDataAddRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountHourMetricsDataTaskReq 广告账户小时指标数据任务请求
type AdAdvertiserAccountHourMetricsDataTaskReq struct {
	g.Meta `path:"/task" tags:"广告账户小时指标数据" method:"post" summary:"广告账户小时指标数据任务"`
	commonApi.Author
	*model.AdAdvertiserAccountHourMetricsDataTaskReq
}

// AdAdvertiserAccountHourMetricsDataTaskRes 广告账户小时指标数据任务返回
type AdAdvertiserAccountHourMetricsDataTaskRes struct {
	commonApi.EmptyRes
}
