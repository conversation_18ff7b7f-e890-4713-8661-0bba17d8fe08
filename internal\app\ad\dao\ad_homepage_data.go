// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-01-24 10:42:38
// 生成路径: internal/app/ad/dao/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adHomepageDataDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adHomepageDataDao struct {
	*internal.AdHomepageDataDao
}

var (
	// AdHomepageData is globally public accessible object for table ad_homepage_data operations.
	AdHomepageData = adHomepageDataDao{
		internal.NewAdHomepageDataDao(),
	}
)

// Fill with you ideas below.
