// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/router/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindAdDesignerMaterialReportController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adDesignerMaterialReport", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdDesignerMaterialReport,
		)
	})
}
