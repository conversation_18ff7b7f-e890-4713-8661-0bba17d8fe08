// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/dao/internal/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAccountSubjectDataStatDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdAccountSubjectDataStatDao struct {
	table   string                          // Table is the underlying table name of the DAO.
	group   string                          // Group is the database configuration group name of current DAO.
	columns AdAccountSubjectDataStatColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdAccountSubjectDataStatColumns defines and stores column names for table ad_account_subject_data_stat.
type AdAccountSubjectDataStatColumns struct {
	CreateDate                  string // 统计日期
	AdvertiserCompany           string // 账户主体
	UserId                      string // 用户ID
	TotalAccounts               string // 总账户数
	ActiveAccounts              string // 在投账户数
	StatCost                    string // 消耗
	StatPayAmount               string // 付费金额（回传时间）
	Active                      string // 激活数
	ClickCnt                    string // 点击数
	ConvertCnt                  string // 转化数
	AttributionGameInAppLtv1Day string // 付费金额
	AttributionMicroGame0DLtv   string // 小程序/小游戏当日LTV
}

var adAccountSubjectDataStatColumns = AdAccountSubjectDataStatColumns{
	CreateDate:                  "create_date",
	AdvertiserCompany:           "advertiser_company",
	UserId:                      "user_id",
	TotalAccounts:               "total_accounts",
	ActiveAccounts:              "active_accounts",
	StatCost:                    "stat_cost",
	StatPayAmount:               "stat_pay_amount",
	Active:                      "active",
	ClickCnt:                    "click_cnt",
	ConvertCnt:                  "convert_cnt",
	AttributionGameInAppLtv1Day: "attribution_game_in_app_ltv_1day",
	AttributionMicroGame0DLtv:   "attribution_micro_game_0d_ltv",
}

// NewAdAccountSubjectDataStatDao creates and returns a new DAO object for table data access.
func NewAdAccountSubjectDataStatDao() *AdAccountSubjectDataStatDao {
	return &AdAccountSubjectDataStatDao{
		group:   "default",
		table:   "ad_account_subject_data_stat",
		columns: adAccountSubjectDataStatColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdAccountSubjectDataStatDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdAccountSubjectDataStatDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdAccountSubjectDataStatDao) Columns() AdAccountSubjectDataStatColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdAccountSubjectDataStatDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdAccountSubjectDataStatDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdAccountSubjectDataStatDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
