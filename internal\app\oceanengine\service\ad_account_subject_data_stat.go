// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/service/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdAccountSubjectDataStat interface {
	List(ctx context.Context, req *model.AdAccountSubjectDataStatSearchReq) (res *model.AdAccountSubjectDataStatSearchRes, err error)
	Add(ctx context.Context, req *model.AdAccountSubjectDataStatAddReq) (err error)
	RunAdAccountSubjectDataStat(ctx context.Context, req *model.AdAccountSubjectDataStatSearchReq) (err error)
	CalcAdAccountSubjectDataStatTask(ctx context.Context)
}

var localAdAccountSubjectDataStat IAdAccountSubjectDataStat

func AdAccountSubjectDataStat() IAdAccountSubjectDataStat {
	if localAdAccountSubjectDataStat == nil {
		panic("implement not found for interface IAdAccountSubjectDataStat, forgot register?")
	}
	return localAdAccountSubjectDataStat
}

func RegisterAdAccountSubjectDataStat(i IAdAccountSubjectDataStat) {
	localAdAccountSubjectDataStat = i
}
