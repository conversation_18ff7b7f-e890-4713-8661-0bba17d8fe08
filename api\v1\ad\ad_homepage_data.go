// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-24 10:42:38
// 生成路径: api/v1/ad/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdHomepageCardDataReq 分页请求参数
type AdHomepageCardDataReq struct {
	g.Meta `path:"/card" tags:"广告首页数据" method:"post" summary:"卡片数据"`
	commonApi.Author
	model.AdHomepageCardDataReq
}

// AdHomepageCardDataRes 列表返回结果
type AdHomepageCardDataRes struct {
	g.Meta `mime:"application/json"`
	*model.AdHomepageCardDataRes
}

// AdHomepageOptimizerPieChartDataReq 优化师饼状图数据请求参数
type AdHomepageOptimizerPieChartDataReq struct {
	g.Meta `path:"/optimizer/piechart" tags:"广告首页数据" method:"post" summary:"优化师饼状图数据"`
	commonApi.Author
	model.AdHomepageOptimizerPieChartDataReq
}

// AdHomepageOptimizerPieChartDataRes 优化师饼状图数据返回结果
type AdHomepageOptimizerPieChartDataRes struct {
	g.Meta `mime:"application/json"`
	*model.AdHomepageOptimizerPieChartDataRes
}

// AdHomepageDesignerPieChartDataReq 设计师饼状图数据请求参数
type AdHomepageDesignerPieChartDataReq struct {
	g.Meta `path:"/designer/piechart" tags:"广告首页数据" method:"post" summary:"设计师饼状图数据"`
	commonApi.Author
	model.AdHomepageDesignerPieChartDataReq
}

// AdHomepageDesignerPieChartDataRes 设计师饼状图数据返回结果
type AdHomepageDesignerPieChartDataRes struct {
	g.Meta `mime:"application/json"`
	*model.AdHomepageDesignerPieChartDataRes
}

// AdHomepageLineChartDataReq 折线图数据请求参数
type AdHomepageLineChartDataReq struct {
	g.Meta `path:"/linechart" tags:"广告首页数据" method:"post" summary:"折线图数据"`
	commonApi.Author
	model.AdHomepageLineChartDataReq
}

// AdHomepageLineChartDataRes 折线图数据返回结果
type AdHomepageLineChartDataRes struct {
	g.Meta `mime:"application/json"`
	*model.AdHomepageLineChartDataRes
}
