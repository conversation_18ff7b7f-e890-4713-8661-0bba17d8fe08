// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-01-03 11:02:30
// 生成路径: internal/app/oceanengine/logic/ad_strategy_config.go
// 生成人：gfast
// desc:巨量广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/generate"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoApi "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
	"time"
)

func init() {
	service.RegisterAdStrategyGenerate(New())
}

func New() service.IAdStrategyGenerate {
	return &sAdStrategyGenerate{}
}

type sAdStrategyGenerate struct{}

func (s *sAdStrategyGenerate) GenerateAdPreview(ctx context.Context, req *model.AdStrategyGenerateReq) (res *model.AdStrategyGenerateRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		generateImpl := generate.GetGenerate(req.BuildMode, req.ProjectGenerateWay, req.PromotionGenerateWay)
		if generateImpl == nil {
			liberr.ErrIsNil(ctx, errors.New("暂不支持该生成方式"))
		}
		estimateInfoMap, err1 := generateImpl.GetEstimateInfo(ctx, req)
		liberr.ErrIsNil(ctx, err1)
		res = &model.AdStrategyGenerateRes{
			EstimateInfoMap: estimateInfoMap,
		}
		res.EstimatePromotionNum = generate.CalcEstimatePromotionNum(estimateInfoMap)
		if req.DataType == commonConsts.DataTypePreview {
			err2 := generate.CheckProjectAtLeastOneAd(estimateInfoMap)
			liberr.ErrIsNil(ctx, err2)
			previewBaseInfoList, err3 := generateImpl.GeneratePreviewBaseInfo(ctx, req, estimateInfoMap)
			liberr.ErrIsNil(ctx, err3)
			advertiserList, err4 := generate.BuildAdvertiserList(ctx, req, estimateInfoMap, previewBaseInfoList)
			liberr.ErrIsNil(ctx, err4)
			generate.BuildProjectAndPromotionName(advertiserList)
			res.AdvertiserList = advertiserList
		}
	})
	return
}

func (s *sAdStrategyGenerate) GetQuota(ctx context.Context, req *model.AdStrategyGetQuotaReq) (res *model.AdStrategyGetQuotaRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		advertiserIdInt64 := gconv.Int64(req.AdvertiserId)
		quotaGetRes, err2 := advertiser.GetToutiaoApiClient().ToolsQuotaGetV2ApiService.
			AccessToken(tokenRes.AccessToken).
			SetRequest(toutiaoApi.ApiOpenApi2ToolsQuotaGetGetRequest{
				AdvertiserId:  &advertiserIdInt64,
				CampaignType:  req.CampaignType,
				DeliveryRange: req.DeliveryRange,
			}).
			Do()
		liberr.ErrIsNil(ctx, err2, "查询在投计划配额失败")
		res = &model.AdStrategyGetQuotaRes{
			ToolsQuotaGetV2ResponseData: quotaGetRes.Data,
		}
	})
	return
}

// UpLoadMaterials 创建项目之前的组装素材的操作
func (s *sAdStrategyGenerate) UpLoadMaterials(ctx context.Context, req *model.AdStrategyGenerateRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, advertiserInfo := range req.AdvertiserList {
			for _, projectInfo := range advertiserInfo.ProjectList {
				for _, promotionInfo := range projectInfo.PromotionList {
					materials := promotionInfo.CreativeMaterials
					if len(materials.Image) > 0 {
						for _, img := range materials.Image {
							mediaId, innerError := adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, img.MaterialId)
							if innerError != nil {
								g.Log().Error(ctx, innerError, "上传Image图片失败！")
							}
							img.MediaId = mediaId
						}
					}
					if len(materials.HorizontalImage) > 0 {
						for _, img := range materials.HorizontalImage {
							mediaId, innerError := adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, img.MaterialId)
							if innerError != nil {
								g.Log().Error(ctx, innerError, "上传HorizontalImage图片失败！")
							}
							img.MediaId = mediaId
						}
					}
					// 竖版图片
					if len(materials.VerticalImage) > 0 {
						for _, img := range materials.VerticalImage {
							mediaId, innerError := adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, img.MaterialId)
							if innerError != nil {
								g.Log().Error(ctx, innerError, "上传VerticalImage图片失败！")
							}
							img.MediaId = mediaId
						}
					}
					// 小图
					if len(materials.SmallImage) > 0 {
						for _, img := range materials.SmallImage {
							mediaId, innerError := adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, img.MaterialId)
							if innerError != nil {
								g.Log().Error(ctx, innerError, "上传SmallImage图片失败！")
							}
							img.MediaId = mediaId
						}
					}
					//var taskIds = make([]int64, 0)
					//var allVideo = make([]*adModel.AdMaterialInfoRes, 0)
					//CREATIVE_IMAGE_MODE_VIDEO 横版视频
					if len(materials.HorizontalVideo) > 0 {
						for _, video := range materials.HorizontalVideo {
							//allVideo = append(allVideo, video)
							exist, mId, _ := adService.AdMaterialUpload().IsExist(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
							if exist {
								video.MediaId = mId
							} else {
								taskId, innerError := adService.AdMaterialUpload().UpLoadVideoAsync(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if innerError != nil {
									g.Log().Error(ctx, innerError, "上传视频失败！")
								}
								video.TaskId = taskId
								//taskIds = append(taskIds, taskId)
							}
							videoThumbnailId := ""
							// 如果是选择的缩略图
							if len(video.VideoThumbnailId) > 0 {
								videoThumbnailId, err = adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if err != nil {
									g.Log().Error(ctx, err, "上传视频缩略图失败！")
								}
							} else { // 如果是使用的缩略图url
								videoThumbnailId, err = adService.AdMaterialUpload().UpLoadByUrl(ctx, advertiserInfo.AdvertiserId, video.ThumbnailUri, video.MaterialName)
								if err != nil {
									g.Log().Error(ctx, err, "上传视频缩略图失败！")
								}
							}
							video.VideoThumbnailId = videoThumbnailId

						}
					}
					// 竖版视频
					if len(materials.VerticalVideo) > 0 {
						for _, video := range materials.VerticalVideo {
							//allVideo = append(allVideo, video)
							exist, mId, _ := adService.AdMaterialUpload().IsExist(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
							if exist {
								video.MediaId = mId
							} else {
								taskId, innerError := adService.AdMaterialUpload().UpLoadVideoAsync(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if innerError != nil {
									g.Log().Error(ctx, innerError, "上传视频失败！")
								}
								video.TaskId = taskId
								//taskIds = append(taskIds, taskId)
							}
							videoThumbnailId := ""
							// 如果是选择的缩略图
							if len(video.VideoThumbnailId) > 0 {
								videoThumbnailId, err = adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if err != nil {
									g.Log().Error(ctx, err, "上传视频缩略图失败！")
								}
							} else { // 如果是使用的缩略图url
								videoThumbnailId, err = adService.AdMaterialUpload().UpLoadByUrl(ctx, advertiserInfo.AdvertiserId, video.ThumbnailUri, video.MaterialName)
								if err != nil {
									g.Log().Error(ctx, err, "上传视频缩略图失败！")
								}
							}
							video.VideoThumbnailId = videoThumbnailId
							//video.MediaId = mediaId
						}
					}
					// 视频
					if len(materials.Video) > 0 {
						for _, video := range materials.Video {
							//allVideo = append(allVideo, video)
							exist, mId, _ := adService.AdMaterialUpload().IsExist(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
							if exist {
								video.MediaId = mId
							} else {
								taskId, innerError := adService.AdMaterialUpload().UpLoadVideoAsync(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if innerError != nil {
									g.Log().Error(ctx, innerError, "上传视频失败！")
								}
								video.TaskId = taskId
								//taskIds = append(taskIds, taskId)
							}
							videoThumbnailId := ""
							// 如果是选择的缩略图
							if len(video.VideoThumbnailId) > 0 {
								videoThumbnailId, err = adService.AdMaterialUpload().UpLoad(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if err != nil {
									g.Log().Error(ctx, err, "上传视频缩略图失败！")
								}
							} else { // 如果是使用的缩略图url
								videoThumbnailId, err = adService.AdMaterialUpload().UpLoadByUrl(ctx, advertiserInfo.AdvertiserId, video.ThumbnailUri, video.MaterialName)
								if err != nil {
									g.Log().Error(ctx, err, "上传视频缩略图失败！")
								}
							}
							video.VideoThumbnailId = videoThumbnailId
							//video.MediaId = mediaId
						}
					}

				}
			}
		}
	})
	return
}

func (s *sAdStrategyGenerate) GetVideoMediaIdAsync(ctx context.Context, req *model.AdStrategyGenerateRes) (needAsync bool, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		needAsync = false
		// 根据参数构建
		for _, advertiserInfo := range req.AdvertiserList {
			for _, projectInfo := range advertiserInfo.ProjectList {
				for _, promotionInfo := range projectInfo.PromotionList {
					materials := promotionInfo.CreativeMaterials
					if len(materials.HorizontalVideo) > 0 {
						for _, video := range materials.HorizontalVideo {
							if len(video.MediaId) > 0 {
								continue
							}
							mediaId, err := adService.AdMaterialUpload().GetUpLoadVideoAsync(ctx, advertiserInfo.AdvertiserId, video.TaskId, video)
							if err != nil {
								g.Log().Error(ctx, err)
								needAsync = true
							} else {
								if len(mediaId) > 0 {
									video.MediaId = mediaId
								} else {
									needAsync = true
								}
							}

						}
					}
					if len(materials.VerticalVideo) > 0 {
						for _, video := range materials.VerticalVideo {
							if len(video.MediaId) > 0 {
								continue
							}
							mediaId, err := adService.AdMaterialUpload().GetUpLoadVideoAsync(ctx, advertiserInfo.AdvertiserId, video.TaskId, video)
							if err != nil {
								g.Log().Error(ctx, err)
								needAsync = true
							} else {
								if len(mediaId) > 0 {
									video.MediaId = mediaId
								} else {
									needAsync = true
								}
							}
						}
					}
					if len(materials.Video) > 0 {
						for _, video := range materials.Video {
							if len(video.MediaId) > 0 {
								continue
							}
							mediaId, err := adService.AdMaterialUpload().GetUpLoadVideoAsync(ctx, advertiserInfo.AdvertiserId, video.TaskId, video)
							if err != nil {
								g.Log().Error(ctx, err)
								needAsync = true
							} else {
								if len(mediaId) > 0 {
									video.MediaId = mediaId
								} else {
									needAsync = true
								}
							}
						}
					}

				}
			}
		}
	})
	return
}

func (s *sAdStrategyGenerate) ExecuteTask(ctx context.Context, req *model.AdExecuteTaskRes) (err error) {
	// 根据参数构建
	var taskId = libUtils.GenerateID()
	err = g.Try(ctx, func(ctx context.Context) {
		req.StrategyConfig.TaskId = taskId
		// 生成配置表格数据
		err = service.AdStrategyConfig().AddTask(ctx, req.StrategyConfig)
		liberr.ErrIsNil(ctx, err, "添加配置表格数据失败")
		// 添加task 数据
		err = service.AdStrategyTask().AddTask(ctx, req)
		liberr.ErrIsNil(ctx, err, "添加配置表格数据失败")

		//生成 projectTask Id
		var pTaskList = make([]*do.AdStrategyTaskProject, 0)
		var promotionList = make([]*do.AdStrategyTaskPromotion, 0)
		var AudienceIdList = make([]int, 0)
		if req.StrategyConfig.ProjectConfig.BaseConfig.LandingType == model.LandingTypeMICRO_GAME && req.StrategyConfig.ProjectConfig.DeliverContentConfig.MicroPromotionType == commonConsts.MicroPromotionTypeBYTE_APP {
			var nowMicroData = new(model.MicroApp)
			for _, advertiserInfo := range req.Generate.AdvertiserList {
				//if len(advertiserInfo.ProjectList) > 0 && advertiserInfo.ProjectList[0].DeliverContentConfig != nil && len(advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData) > 0 {
				//	nowMicroData = advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData[0]
				//} else {
				//	liberr.ErrIsNil(ctx, err, "投放内容与目标不能为空！")
				//}
				if len(advertiserInfo.ProjectList) == 0 {
					liberr.ErrIsNil(ctx, errors.New("project list 为空"), "投放内容与目标不能为空！")
				}
				project := advertiserInfo.ProjectList[0]
				if project.DeliverContentConfig == nil || len(project.DeliverContentConfig.MicroData) == 0 {
					liberr.ErrIsNil(ctx, errors.New("micro data 为空"), "投放内容与目标不能为空！")
				}
				nowMicroData = project.DeliverContentConfig.MicroData[0]
				trackTypes := make([]*toutiaoModels.EventManagerEventConfigsGetV2DataEventConfigsTrackTypes, 0)
				if advertiserInfo.ProjectList[0].DeliverContentConfig.TrackTypes != nil {
					for _, trackType := range advertiserInfo.ProjectList[0].DeliverContentConfig.TrackTypes {
						tType := toutiaoModels.EventManagerEventConfigsGetV2DataEventConfigsTrackTypes(trackType)
						trackTypes = append(trackTypes, &tType)
					}
				}
				// 获取当前广告主下的资产
				tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserInfo.AdvertiserId)
				liberr.ErrIsNil(ctx, err1, "获取access_token失败")
				accessToken := tokenRes.AccessToken
				advertiserIdInt64 := gconv.Int64(advertiserInfo.AdvertiserId)
				var pageNo int32 = 1
				var pageSize int32 = 100
				assetsListRes, err2 := advertiser.GetToutiaoApiClient().ToolsEventAllAssetsListGetV2ApiService.
					AccessToken(accessToken).
					ToolsEventAllAssetsListGetV2Request(toutiaoApi.ToolsEventAllAssetsListGetV2Request{
						AdvertiserId: &advertiserIdInt64,
						Filtering: &toutiaoModels.ToolsEventAllAssetsListV2Filtering{
							AssetType: toutiaoModels.MINI_PROGRAME_ToolsEventAllAssetsListV2FilteringAssetType.Ptr(),
						},
						Page:     &pageNo,
						PageSize: &pageSize,
					}).Do()
				liberr.ErrIsNil(ctx, err2)
				if assetsListRes.Data == nil || assetsListRes.Data.AssetList == nil || len(assetsListRes.Data.AssetList) == 0 {
					// 创建资产
					g.Log().Error(ctx, fmt.Sprintf("没有找到任何资产！appId:%v aid:%v", advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData[0].AppId, advertiserInfo.AdvertiserId))
					err = CreateEventAsset(ctx, accessToken, advertiserIdInt64, nowMicroData, advertiserInfo, trackTypes)
					continue
				}
				assetIds := make([]int64, 0)
				for _, asset := range assetsListRes.Data.AssetList {
					assetIds = append(assetIds, *asset.AssetId)
				}
				// 获取资产详情
				assetsDetailRes, err3 := advertiser.GetToutiaoApiClient().ToolsEventAllAssetsDetailGetV2ApiService.
					AccessToken(accessToken).
					ToolsEventAllAssetsDetailGetV2Request(toutiaoApi.ToolsEventAllAssetsDetailGetV2Request{
						AdvertiserId: &advertiserIdInt64,
						AssetIds:     &assetIds,
					}).Do()
				liberr.ErrIsNil(ctx, err3)
				var sid = int64(0)
				for _, detail := range assetsDetailRes.Data.AssetList {
					//获取当前小程序的资产id
					if *detail.MicroAppId == advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData[0].AppId {
						sid = *detail.AssetId
						advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData[0].AssetId = *detail.AssetId
					}
				}
				if sid > 0 {
					// 获取资产下已创建事件列表
					eventConfigsRes, err4 := advertiser.GetToutiaoApiClient().EventManagerEventConfigsGetV2ApiService.
						AccessToken(accessToken).
						EventManagerEventConfigsGetV2Request(toutiaoApi.EventManagerEventConfigsGetV2Request{
							AdvertiserId: &advertiserIdInt64,
							AssetId:      &sid,
						}).Do()
					liberr.ErrIsNil(ctx, err4)
					if eventConfigsRes.Data == nil || eventConfigsRes.Data.EventConfigs == nil || len(eventConfigsRes.Data.EventConfigs) == 0 {
						// 资产下创建事件
						err = CreateAsset(ctx, accessToken, advertiserIdInt64, sid, advertiserInfo, trackTypes)
						if err != nil {
							liberr.ErrIsNil(ctx, err)
						}
						if advertiserInfo.ProjectList[0].DeliverContentConfig.DeepExternalId > 0 {
							err = CreateDeepAsset(ctx, accessToken, advertiserIdInt64, sid, advertiserInfo, trackTypes)
							if err != nil {
								liberr.ErrIsNil(ctx, err)
							}
						}
					} else {
						// 判断当前资产事件是否已经存在
						var haveEvent = false
						for _, eventConfig := range eventConfigsRes.Data.EventConfigs {
							if *eventConfig.EventId == advertiserInfo.ProjectList[0].DeliverContentConfig.EventId {
								for _, trackType := range trackTypes {
									var haveType = false
									for _, types := range eventConfig.TrackTypes {
										if string(*types) == string(*trackType) {
											haveType = true
										}
									}
									if !haveType {
										err = errors.New("创建项目失败:\n「优化目标」已有事件「激活」的回传方式与搭建所选「小程序API回传」不一致。")
										liberr.ErrIsNil(ctx, err, "创建项目失败:\n「优化目标」已有事件「激活」的回传方式与搭建所选「小程序API回传」不一致。！")
									}
								}
								haveEvent = true
							}
						}
						if !haveEvent {
							err = CreateAsset(ctx, accessToken, advertiserIdInt64, sid, advertiserInfo, trackTypes)
							if err != nil {
								liberr.ErrIsNil(ctx, err)
							}
						}
						if advertiserInfo.ProjectList[0].DeliverContentConfig.DeepExternalId > 0 {
							var haveDeepEvent = false
							for _, eventConfig := range eventConfigsRes.Data.EventConfigs {
								if *eventConfig.EventId == advertiserInfo.ProjectList[0].DeliverContentConfig.DeepExternalId {
									for _, trackType := range trackTypes {
										var haveType = false
										for _, types := range eventConfig.TrackTypes {
											if string(*types) == string(*trackType) {
												haveType = true
											}
										}
										if !haveType {
											err = errors.New("创建项目失败:\n「优化目标」已有事件「激活」的回传方式与搭建所选「小程序API回传」不一致。")
											liberr.ErrIsNil(ctx, err, "创建项目失败:\n「优化目标」已有事件「激活」的回传方式与搭建所选「小程序API回传」不一致。！")
										}
									}
									haveDeepEvent = true
								}
							}
							if !haveDeepEvent {
								err = CreateDeepAsset(ctx, accessToken, advertiserIdInt64, sid, advertiserInfo, trackTypes)
								if err != nil {
									liberr.ErrIsNil(ctx, err)
								}
							}
						}
					}
				} else {
					g.Log().Error(ctx, fmt.Sprintf("没有找到任何资产！appId:%v aid:%v", advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData[0].AppId, advertiserInfo.AdvertiserId))
					err = CreateEventAsset(ctx, accessToken, advertiserIdInt64, nowMicroData, advertiserInfo, trackTypes)

				}
			}
			time.Sleep(5 * time.Second)
		}

		// 如果根据是模板生成的流程
		if req.StrategyConfig.LandingPage.LandingPageType == commonConsts.LandingPageTemple {
			var landingPageCreateList = make([]*model.LandingPageTempCreate, 0)
			for _, adInfo := range req.Generate.AdvertiserList {
				for _, proInfo := range adInfo.ProjectList {
					for _, promotionInfo := range proInfo.PromotionList {
						var haveIn = false
						for _, create := range landingPageCreateList {
							if create.InstanceId == proInfo.DeliverContentConfig.MicroData[0].InstanceId && create.TemplateID == promotionInfo.LandingPage.SelectLandingPageInfoList[0].TemplateID && create.SiteID == adInfo.AdvertiserId {
								haveIn = true
							}
						}
						if !haveIn {
							landingPageCreateList = append(landingPageCreateList, &model.LandingPageTempCreate{
								AdvertiserID: adInfo.AdvertiserId,
								TemplateID:   promotionInfo.LandingPage.SelectLandingPageInfoList[0].TemplateID,
								InstanceId:   proInfo.DeliverContentConfig.MicroData[0].InstanceId,
							})
						}
					}
				}
			}
			//for _, landingPageInfo := range req.StrategyConfig.LandingPage.LandingPageData {
			//	for _, pageInfo := range landingPageInfo.SelectLandingPageInfoList {
			//		landingPageCreateList = append(landingPageCreateList, &model.LandingPageTempCreate{
			//			AdvertiserID: landingPageInfo.AdvertiserID,
			//			TemplateID:   pageInfo.TemplateID,
			//		})
			//	}
			//}
			landingPageCreateList, err = adService.AdLandingPageTemp().CreateByLandingPageTempCreate(ctx, landingPageCreateList)
			if err != nil {
				liberr.ErrIsNil(ctx, err, "创建落地页失败："+err.Error())
			}
			// 添加投放目的 资产 事件
			// IsBYTEAppOrGame 当landing_type = MICRO_GAME且micro_promotion_type = BYTE_APP或BYTE_GAME 判断当前是否是字节小程序

			// 休眠 5-10秒
			time.Sleep(5 * time.Second)
			// 给siteId url 赋值
			for _, aInfo := range req.Generate.AdvertiserList {
				for _, projectInfo := range aInfo.ProjectList {
					for _, promotionInfo := range projectInfo.PromotionList {
						for _, landingPageTempCreate := range landingPageCreateList {
							if aInfo.AdvertiserId == landingPageTempCreate.AdvertiserID && landingPageTempCreate.TemplateID == promotionInfo.LandingPage.SelectLandingPageInfoList[0].TemplateID {
								promotionInfo.LandingPage.SelectLandingPageInfoList[0].SiteID = landingPageTempCreate.SiteID
								promotionInfo.LandingPage.SelectLandingPageInfoList[0].SiteURL = fmt.Sprintf("https://www.chengzijianzhan.com/tetris/page/%s/", landingPageTempCreate.SiteID)
							}
						}
					}
				}
			}

		}

		for _, item := range req.Generate.AdvertiserList {
			var projectNum = len(item.ProjectList)
			var promotionNum int
			for _, pro := range item.ProjectList {
				promotionNum += len(pro.PromotionList)
				pro.TaskProjectId = libUtils.GenerateID()
				var capBid, deepBid = float64(0), float64(0)
				if len(pro.BidConfig.BidData) > 0 && len(pro.BidConfig.BidData[0].BidDataList) > 0 {
					capBid = pro.BidConfig.BidData[0].BidDataList[0].CpaBid
					deepBid = pro.BidConfig.BidData[0].BidDataList[0].DeepCpaBid
				}
				if pro.AudiencePackageConfig != nil {
					for _, datum := range pro.AudiencePackageConfig.AudienceData {
						for _, audiencePackage := range datum.SelectAudiencePackageList {
							AudienceIdList = append(AudienceIdList, int(audiencePackage.Id))
						}
					}
				}

				if req.StrategyConfig.BuildMode == string(commonConsts.ExistProject) {
					pTaskList = append(pTaskList, &do.AdStrategyTaskProject{
						TaskProjectId:      pro.TaskProjectId,
						ProjectId:          pro.BaseConfig.ProjectId,
						ProjectName:        pro.BaseConfig.ProjectName,
						AdvertiserId:       item.AdvertiserId,
						AdvertiserNick:     item.AdvertiserNick,
						ExternalAction:     pro.DeliverContentConfig.ExternalAction,
						DeepExternalAction: pro.DeliverContentConfig.DeepExternalAction,
						CpaBid:             capBid,
						DeepCpabid:         deepBid,
						PromotionNum:       len(pro.PromotionList),
						Status:             model.TaskProjectStatusSuccess,
						ProjectData:        nil,
						TaskId:             taskId,
						CreatedAt:          gtime.Now(),
					})
				} else {
					pTaskList = append(pTaskList, &do.AdStrategyTaskProject{
						TaskProjectId: pro.TaskProjectId,
						//ProjectId:          nil,
						ProjectName:        pro.BaseConfig.ProjectName,
						AdvertiserId:       item.AdvertiserId,
						AdvertiserNick:     item.AdvertiserNick,
						ExternalAction:     pro.DeliverContentConfig.ExternalAction,
						DeepExternalAction: pro.DeliverContentConfig.DeepExternalAction,
						CpaBid:             capBid,
						DeepCpabid:         deepBid,
						PromotionNum:       len(pro.PromotionList),
						Status:             model.TaskProjectStatusInit,
						ProjectData:        nil,
						TaskId:             taskId,
						CreatedAt:          gtime.Now(),
					})
				}

				// 构建广告数据
				for _, promotionItem := range pro.PromotionList {
					promotionItem.TaskPromotionId = libUtils.GenerateID()
					if len(promotionItem.BidConfig.BidData) > 0 && len(promotionItem.BidConfig.BidData[0].BidDataList) > 0 {
						capBid = promotionItem.BidConfig.BidData[0].BidDataList[0].CpaBid
						deepBid = promotionItem.BidConfig.BidData[0].BidDataList[0].DeepCpaBid
					}
					// todo 添加广告素材
					promotionList = append(promotionList, &do.AdStrategyTaskPromotion{
						//PromotionId:   nil,
						PromotionName: promotionItem.PromotionName,
						CpaBid:        capBid,
						DeepCpabid:    deepBid,
						Status:        model.TaskPromotionStatusInit,
						TaskId:        taskId,
						//ErrMsg:        nil,
						//PromotionId:     nil,
						TaskProjectId:   pro.TaskProjectId,
						TaskPromotionId: promotionItem.TaskPromotionId,
						PromotionData:   nil,
						CreatedAt:       gtime.Now(),
					})

				}
			}
			if strings.Contains(req.StrategyConfig.ProjectConfig.BaseConfig.ProjectName, generate.DailyNum) {
				generate.IncrProjectDailyNum(item.AdvertiserId, projectNum)
			}
			if strings.Contains(req.StrategyConfig.ProjectConfig.BaseConfig.ProjectName, generate.DailyNum) {
				generate.IncrPromotionDailyNum(item.AdvertiserId, promotionNum)
			}
		}
		// 判断任务是否中断
		if !CheckTaskStatus(ctx, taskId) {
			// 更新当前任务状态
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusTerminated))
			return
		} else {
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusMaterialProcessing))
		}
		//  异步上传素材
		err = s.UpLoadMaterials(ctx, req.Generate)
		if err != nil {
			liberr.ErrIsNil(ctx, err, "上传素材出错 err:"+err.Error())
		}

		err = service.AdStrategyTaskProject().Save(ctx, pTaskList)
		if err != nil {
			liberr.ErrIsNil(ctx, err, "添加配置表格数据失败err : "+err.Error())
		}

		// 添加广告配置数据
		err = service.AdStrategyTaskPromotion().Save(ctx, promotionList)
		if err != nil {
			liberr.ErrIsNil(ctx, err, "添加广告配置数据失败err : "+err.Error())
		}
		// 获取上传视频
		var maxRetries = 5
		var retryCount = 0
		needAsync := true
		// 当需要异步执行时进行重试
		for needAsync && retryCount < maxRetries {
			needAsync, _ = s.GetVideoMediaIdAsync(ctx, req.Generate)
			// 如果没有错误，跳出循环
			if !needAsync {
				break
			}
			// 增加重试次数
			retryCount++
			// 可以在这里添加适当的延迟，避免频繁重试
			time.Sleep(5 * time.Second)
		}

		// 判断任务是否中断
		if !CheckTaskStatus(ctx, taskId) {
			// 更新当前任务状态
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusTerminated))
			return
		} else {
			// 添加修改任务状态
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusProjectInit))
		}
		var packageList []*model.AdAssetAudiencePackageInfoRes
		// 根据id查询 sAdAssetAudiencePackage
		if len(AudienceIdList) > 0 {
			packageList, err = service.AdAssetAudiencePackage().GetByIds(ctx, AudienceIdList)
			if err != nil {
				liberr.ErrIsNil(ctx, err, "AdAssetAudiencePackage().GetByIds失败err : "+err.Error())
				g.Log().Error(ctx, err)
			}
		}

		//adService.AdLandingPageTemp().GetByIds(ctx,)
		// 已经存在的项目跳过创建项目的步骤
		if req.StrategyConfig.BuildMode != string(commonConsts.ExistProject) {
			// 添加 project 项目数据  首先根据req 构建project 数据
			for _, advertiserInfo := range req.Generate.AdvertiserList {
				// 创建项目
				ids, taskList := generate.CreateProject(ctx, advertiserInfo, packageList)
				for _, project := range pTaskList {
					if _, ok := ids[gconv.String(project.TaskProjectId)]; ok {
						project.ProjectId = ids[gconv.String(project.TaskProjectId)]
						project.Status = model.TaskProjectStatusSuccess
					}
					if v, ok := taskList[gconv.String(project.TaskProjectId)]; ok {
						data, marshalErr := json.Marshal(v)
						if marshalErr == nil {
							project.ProjectData = data
						}
					}
				}

			}
			// 更新 项目数据
			err = service.AdStrategyTaskProject().Save(ctx, pTaskList)
		}
		// 判断任务是否中断
		if !CheckTaskStatus(ctx, taskId) {
			// 更新当前任务状态
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusTerminated))
			return
		} else {
			// 添加修改任务状态
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusPromotionInit))
		}
		adMaterialPromotionAddReq := make([]*model.AdMaterialPromotionAddReq, 0)
		userId := sysService.Context().GetUserId(ctx)
		// 创建广告
		for _, advertiserInfo := range req.Generate.AdvertiserList {
			// 创建项目
			ids, taskList, idsMap := generate.CreatePromotion(ctx, advertiserInfo)
			for _, item := range promotionList {
				if v, ok := ids[gconv.String(item.TaskPromotionId)]; ok {
					if v.Data != nil && *v.Data.PromotionId > 0 {
						item.PromotionId = *v.Data.PromotionId
						item.Status = model.TaskPromotionStatusSuccess
					} else {
						item.ErrMsg = v.Message
						item.Status = model.TaskPromotionStatusError
					}
				}

				if v, ok := taskList[gconv.String(item.TaskPromotionId)]; ok {
					data, marshalErr := json.Marshal(v)
					if marshalErr == nil {
						item.PromotionData = data
					}
				}

				if v, ok := idsMap[gconv.String(item.TaskPromotionId)]; ok {
					adMaterialPromotionAddReq = make([]*model.AdMaterialPromotionAddReq, 0)
					for _, materlIds := range v {
						adMaterialPromotionAddReq = append(adMaterialPromotionAddReq, &model.AdMaterialPromotionAddReq{
							MediaId:       materlIds.MediaId,
							MaterialId:    materlIds.MaterialId,
							PromotionId:   gconv.String(item.PromotionId),
							PromotionName: gconv.String(item.PromotionName),
							UserId:        int(userId),
						})
					}
					// 添加日志
					g.Log().Info(ctx, fmt.Sprintf("AdMaterialPromotion sAdMaterialPromotionAddReq:%+v", adMaterialPromotionAddReq))
					err = service.AdMaterialPromotion().AddList(ctx, adMaterialPromotionAddReq)
					if err != nil {
						g.Log().Error(ctx, err)
					}
				}
			}
		}
		// 更新项目id 广告id
		err = service.AdStrategyTaskPromotion().Save(ctx, promotionList)
		if err == nil {
			_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusFINISH))
		}
	})
	if err != nil {
		_ = service.AdStrategyTask().UpdateStatus(ctx, taskId, string(model.TaskStatusError))
	}
	return
}

// CreateAsset 创建事件资产
func CreateAsset(ctx context.Context, accessToken string, advertiserIdInt64 int64, AssetId int64, advertiserInfo *model.AdvertiserInfo, trackTypes []*toutiaoModels.EventManagerEventConfigsGetV2DataEventConfigsTrackTypes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		eventRes, err := advertiser.GetToutiaoApiClient().EventManagerAvailableEventsGetV2ApiService.
			AccessToken(accessToken).SetReq(&toutiaoApi.ApiOpenApi2EventManagerAvailableEventsGetGetRequest{
			AdvertiserId: &advertiserIdInt64,
			AssetId:      &AssetId,
		}).Do()
		liberr.ErrIsNil(ctx, err, "获取可创建事件列表失败")
		if len(eventRes.Data.EventConfigs) > 0 {
			for _, eventInfo := range eventRes.Data.EventConfigs {
				g.Log().Info(ctx, fmt.Sprintf("------------------获取可创建事件列表 EventType：%s ,EventId:%v ,ExternalAction:%v ----------", *eventInfo.EventType, *eventInfo.EventId, advertiserInfo.ProjectList[0].DeliverContentConfig.ExternalAction))
				if len(advertiserInfo.ProjectList) == 0 {
					liberr.ErrIsNil(ctx, errors.New("project list 为空"), "项目列表为空")
				}
				deliverConfig := advertiserInfo.ProjectList[0].DeliverContentConfig
				if deliverConfig == nil {
					liberr.ErrIsNil(ctx, errors.New("deliver content config 为空"), "投放内容配置为空")
				}

				if *eventInfo.EventId == advertiserInfo.ProjectList[0].DeliverContentConfig.EventId {
					// 创建资产事件
					eventCreateRes, err := advertiser.GetToutiaoApiClient().EventManagerEventsCreateV2ApiService.AccessToken(accessToken).SetReq(toutiaoModels.EventManagerEventsCreateV2Request{
						AdvertiserId: advertiserIdInt64,
						AssetId:      AssetId,
						EventId:      *eventInfo.EventId,
						TrackTypes:   trackTypes,
					}).Do()
					liberr.ErrIsNil(ctx, err, "创建资产事件失败")
					g.Log().Info(ctx, fmt.Sprintf("创建资产事件成功：%s", eventCreateRes.Data))
				}
			}
		}
	})
	return
}

func CreateDeepAsset(ctx context.Context, accessToken string, advertiserIdInt64 int64, AssetId int64, advertiserInfo *model.AdvertiserInfo, trackTypes []*toutiaoModels.EventManagerEventConfigsGetV2DataEventConfigsTrackTypes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		eventRes, err := advertiser.GetToutiaoApiClient().EventManagerAvailableEventsGetV2ApiService.
			AccessToken(accessToken).SetReq(&toutiaoApi.ApiOpenApi2EventManagerAvailableEventsGetGetRequest{
			AdvertiserId: &advertiserIdInt64,
			AssetId:      &AssetId,
		}).Do()
		liberr.ErrIsNil(ctx, err, "获取可创建事件列表失败")
		if len(eventRes.Data.EventConfigs) > 0 {
			for _, eventInfo := range eventRes.Data.EventConfigs {
				g.Log().Info(ctx, fmt.Sprintf("------------------获取可创建事件列表 EventType：%s ,EventId:%v ,ExternalAction:%v ----------", *eventInfo.EventType, *eventInfo.EventId, advertiserInfo.ProjectList[0].DeliverContentConfig.ExternalAction))
				if *eventInfo.EventId == advertiserInfo.ProjectList[0].DeliverContentConfig.DeepExternalId {
					// 创建资产事件
					eventCreateRes, err := advertiser.GetToutiaoApiClient().EventManagerEventsCreateV2ApiService.AccessToken(accessToken).SetReq(toutiaoModels.EventManagerEventsCreateV2Request{
						AdvertiserId: advertiserIdInt64,
						AssetId:      AssetId,
						EventId:      *eventInfo.EventId,
						TrackTypes:   trackTypes,
					}).Do()
					liberr.ErrIsNil(ctx, err, "创建深度资产事件失败")
					g.Log().Info(ctx, fmt.Sprintf("创建深度资产事件成功：%s", eventCreateRes.Data))
				}
			}
		}
	})
	return
}

// CreateEventAsset 创建事件资产同时在资产下创建事件
func CreateEventAsset(ctx context.Context, accessToken string, advertiserIdInt64 int64, nowMicroData *model.MicroApp, advertiserInfo *model.AdvertiserInfo, trackTypes []*toutiaoModels.EventManagerEventConfigsGetV2DataEventConfigsTrackTypes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 首先创建小程序资产
		createRes, err := advertiser.GetToutiaoApiClient().EventManagerAssetsCreateV2ApiService.
			AccessToken(accessToken).
			SetReq(toutiaoModels.EventManagerAssetsCreateV2Request{
				AdvertiserId: advertiserIdInt64,
				MiniProgramAsset: &toutiaoModels.EventManagerAssetsCreateV2RequestMiniProgramAsset{
					InstanceId:      gconv.Int64(nowMicroData.InstanceId),
					MiniProgramId:   nowMicroData.AppId,
					MiniProgramName: nowMicroData.Name,
					MiniProgramType: toutiaoModels.BYTE_APP_EventManagerAssetsCreateV2MiniProgramAssetMiniProgramType,
				},
				AssetType: toutiaoModels.MINI_PROGRAME_EventManagerAssetsCreateV2AssetType,
			}).Do()
		liberr.ErrIsNil(ctx, err, "创建资产失败")
		advertiserInfo.ProjectList[0].DeliverContentConfig.MicroData[0].AssetId = *createRes.Data.AssetId
		// 创建事件资产
		err = CreateAsset(ctx, accessToken, advertiserIdInt64, *createRes.Data.AssetId, advertiserInfo, trackTypes)
		if err != nil {
			liberr.ErrIsNil(ctx, err)
		}
		if advertiserInfo.ProjectList[0].DeliverContentConfig.DeepExternalId > 0 {
			err = CreateDeepAsset(ctx, accessToken, advertiserIdInt64, *createRes.Data.AssetId, advertiserInfo, trackTypes)
			if err != nil {
				liberr.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// CheckTaskStatus 判断当前任务是否已经中止
func CheckTaskStatus(ctx context.Context, taskId string) (pass bool) {
	pass = true
	err := g.Try(ctx, func(ctx context.Context) {
		res, innerErr := service.AdStrategyTask().GetByTaskId(ctx, taskId)
		liberr.ErrIsNil(ctx, innerErr, "获取任务失败")
		if res.TaskStatus == model.TaskStatusTerminated {
			pass = false
			return
		}
	})
	liberr.ErrIsNil(ctx, err, "获取任务失败")
	return
}
