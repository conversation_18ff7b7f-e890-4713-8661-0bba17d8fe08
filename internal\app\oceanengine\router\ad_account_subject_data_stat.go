// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/router/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/controller"
)

func (router *Router) BindAdAccountSubjectDataStatController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adAccountSubjectDataStat", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdAccountSubjectDataStat,
		)
	})
}
