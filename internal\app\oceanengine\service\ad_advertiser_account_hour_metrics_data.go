// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/service/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdAdvertiserAccountHourMetricsData interface {
	List(ctx context.Context, req *model.AdAdvertiserAccountHourMetricsDataSearchReq) (res *model.AdAdvertiserAccountHourMetricsDataSearchRes, err error)
	Add(ctx context.Context, req *model.AdAdvertiserAccountHourMetricsDataAddReq) (err error)
	RunSyncAdAdvertiserAccountHourMetricsData(ctx context.Context, req *model.AdAdvertiserAccountHourMetricsDataTaskReq) (err error)
	SyncAdAdvertiserAccountHourMetricsDataTask(ctx context.Context)
}

var localAdAdvertiserAccountHourMetricsData IAdAdvertiserAccountHourMetricsData

func AdAdvertiserAccountHourMetricsData() IAdAdvertiserAccountHourMetricsData {
	if localAdAdvertiserAccountHourMetricsData == nil {
		panic("implement not found for interface IAdAdvertiserAccountHourMetricsData, forgot register?")
	}
	return localAdAdvertiserAccountHourMetricsData
}

func RegisterAdAdvertiserAccountHourMetricsData(i IAdAdvertiserAccountHourMetricsData) {
	localAdAdvertiserAccountHourMetricsData = i
}
