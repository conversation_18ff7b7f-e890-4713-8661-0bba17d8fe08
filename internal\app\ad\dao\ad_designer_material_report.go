// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/dao/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adDesignerMaterialReportDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adDesignerMaterialReportDao struct {
	*internal.AdDesignerMaterialReportDao
}

var (
	// AdDesignerMaterialReport is globally public accessible object for table tools_gen_table operations.
	AdDesignerMaterialReport = adDesignerMaterialReportDao{
		internal.NewAdDesignerMaterialReportDao(),
	}
)

// Fill with you ideas below.
