package advertiser

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/gogf/gf/v2/frame/g"
	oceanService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"reflect"

	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

type ToutiaoAdService struct {
	OrganizationIds []int64 `json:"organizationIds"`
	AgentIds        []int64 `json:"agentIds"`
	AppId           int64   `json:"appId"`
	Secret          string  `json:"secret"`
}

var (
	instance       *toutiao.APIClient
	once           sync.Once
	toutiaoService *ToutiaoAdService
	//client         *resty.Client
)

func init() {
	toutiaoService = new(ToutiaoAdService)
	g.Cfg().MustGet(context.Background(), "advertiser.toutiao").Scan(&toutiaoService)
}

func GetToutiaoApiClient() *toutiao.APIClient {
	once.Do(func() {
		configuration := NewConfiguration()
		client := resty.New()
		client.SetTimeout(5 * time.Second) //设置全局超时时间
		client.SetTransport(&http.Transport{
			MaxIdleConnsPerHost:   10,               // 对于每个主机，保持最大空闲连接数为 10
			IdleConnTimeout:       30 * time.Second, // 空闲连接超时时间为 30 秒
			TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时时间为 10 秒
			ResponseHeaderTimeout: 20 * time.Second, // 等待响应头的超时时间为 20 秒
		})
		configuration.HTTPClient = client
		instance = toutiao.NewAPIClient(configuration)
	})
	return instance
}

const ocAccessToken = "OCEANENGINE:ACCESS:THEATER:TOKEN:"

//const ocREAccessToken = "OCEANENGINE:ACCESS:THEATER:REFRESH:TOKEN:"

// GetAccessTokenByAppIdCache 获取appToken
func GetAccessTokenByAppIdCache() (token string) {
	//查询广告主所属商务账号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	aId := g.Cfg().MustGet(ctx, "advertiser.toutiao.appId").Int64()
	secret := g.Cfg().MustGet(ctx, "advertiser.toutiao.secret").String()
	if len(secret) == 0 {
		g.Log().Error(ctx, "advertiser.toutiao.secret 检测到配置为空")
		return ""
	}
	token = commonService.GetGoRedis().Get(ctx, fmt.Sprintf("%s%v", ocAccessToken, aId)).Val()
	if len(token) == 0 {
		oauth2Response, err1 := GetToutiaoApiClient().GetOauth2AppAccessTokenApiService.
			Oauth2AppAccessTokenRequest(GetOauth2AppAccessTokenRequest{
				AppId:  aId,
				Secret: secret,
			}).Do()
		if err1 != nil {
			g.Log().Error(ctx, err1)
			return
		}
		token = oauth2Response.Data.AccessToken
		// 设置token
		var accessToken = oauth2Response.Data.AccessToken
		var expiresIn = oauth2Response.Data.ExpiresIn - 100
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", ocAccessToken, aId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
	}
	token = strings.ReplaceAll(token, `"`, "")
	return
}

func GetToutiaoAccessTokenByAdId(advertiserId int64) string {
	for _, agentId := range toutiaoService.AgentIds {
		has := commonService.GetGoRedis().SIsMember(context.Background(), fmt.Sprintf("%s%v", OceanengineAdvertiserList, agentId), fmt.Sprintf("%v", advertiserId)).Val()
		if has {
			if agentId > 0 {
				var accessToken = commonService.GetGoRedis().Get(context.Background(), fmt.Sprintf("%s%v", OceanengineAccessToken, agentId)).Val()
				if accessToken != "" {
					accessToken = strings.ReplaceAll(accessToken, `"`, "")
					return accessToken
				}
			}
		}
	}
	for _, organizationId := range toutiaoService.OrganizationIds {
		//g.Log().Info(context.Background(), fmt.Sprintf("------------- SISMEMBER %+v %v  --------------------", fmt.Sprintf("%s%v", OceanengineAdvertiserList, organizationId), advertiserId))
		has := commonService.GetGoRedis().SIsMember(context.Background(), fmt.Sprintf("%s%v", OceanengineAdvertiserList, organizationId), fmt.Sprintf("%v", advertiserId)).Val()
		if has {
			if organizationId > 0 {
				//g.Log().Info(context.Background(), fmt.Sprintf("------------- 获取token has: %+v   --------------------", fmt.Sprintf("%s%v", OceanengineAccessToken, organizationId)))
				var cmdStr = commonService.GetGoRedis().Get(context.Background(), fmt.Sprintf("%s%v", OceanengineAccessToken, organizationId))
				//g.Log().Info(context.Background(), fmt.Sprintf("------------- 获取token respones: %+v   --------------------", cmdStr))
				if cmdStr != nil {
					accessToken := cmdStr.Val()
					accessToken = strings.ReplaceAll(accessToken, `"`, "")
					return accessToken
				}
				return ""
			} else {
				return ""
			}
		}
	}
	return ""
}

// Oauth2RefreshTokenApi 刷新Refresh Token
func Oauth2RefreshTokenApi(ctx context.Context) {
	//var retryCount = 3
	advertiserIds := make([]int64, 0)
	advertiserIds = append(advertiserIds, toutiaoService.OrganizationIds...)
	advertiserIds = append(advertiserIds, toutiaoService.AgentIds...)
	for i := 0; i < len(advertiserIds); i++ {
		var refreshToken = commonService.GetGoRedis().Get(ctx, fmt.Sprintf("%s%v", OceanengineRefreshToken, advertiserIds[i])).Val()
		if libUtils.IsNullOrEmpty(refreshToken) {
			g.Log().Info(ctx, fmt.Sprintf("------------- Oauth2RefreshTokenApi 未获取到 OrganizationId: %v    --------------------", advertiserIds[i]))
			continue
		}
		refreshToken = strings.ReplaceAll(refreshToken, `"`, "")
		var request Oauth2RefreshTokenRequest
		request.AppId = toutiaoService.AppId
		request.Secret = toutiaoService.Secret
		request.RefreshToken = refreshToken
		//request.AppId = int64(1775636985554964)
		//request.Secret = "4eb3b2b2d6390120fe16c6e542a4b74abcdf8ab6"
		//request.RefreshToken = "22610c88a4eb6f73e8150c180938a4152a0f6744"
		resp, err := GetToutiaoApiClient().Oauth2RefreshTokenApi.SetRequest(request).Do()
		//接口频率控制流程
		if err != nil {
			if strings.Contains(err.Error(), "Too many requests") {
				g.Log().Info(ctx, fmt.Sprintf("-------------  执行 Oauth2RefreshTokenApi Too many requests  err:%v -------------------", err))
				time.Sleep(2 * time.Second)
				resp, err = GetToutiaoApiClient().Oauth2RefreshTokenApi.SetRequest(request).Do()
			}
		}
		if err != nil || resp == nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- Oauth2RefreshTokenApi 刷新RefreshToken ERROR refreshToken: %v err：%v,resp:%v  --------------------", refreshToken, err, resp))
			continue
		}
		g.Log().Info(ctx, fmt.Sprintf("------------- Oauth2RefreshTokenApi 刷新RefreshToken END 结束 resp.refreshToken: %v ,resp.AccessToken: %v, err：%v, resp:%+v  --------------------", resp.RefreshToken, resp.AccessToken, err, resp))
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", OceanengineRefreshToken, advertiserIds[i]), fmt.Sprintf(`"%s"`, resp.RefreshToken), time.Duration(resp.RefreshTokenExpiresIn)*time.Second)
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", OceanengineAccessToken, advertiserIds[i]), fmt.Sprintf(`"%s"`, resp.AccessToken), time.Duration(resp.ExpiresIn)*time.Second)
	}
	_ = oceanService.AdMajordomoAdvertiserAccount().RefreshAccessTokenTask(ctx)
}

// MajordomoAdvertiserSelectV2Api 获取纵横组织下资产账户列表
func MajordomoAdvertiserSelectV2Api(ctx context.Context) {
	for _, organizationId := range toutiaoService.OrganizationIds {
		var accessToken = commonService.GetGoRedis().Get(ctx, fmt.Sprintf("%s%v", OceanengineAccessToken, organizationId)).Val()
		if libUtils.IsNullOrEmpty(accessToken) {
			continue
		}
		accessToken = strings.ReplaceAll(accessToken, `"`, "")
		//advertiserId, _ := strconv.ParseInt(organizationId, 10, 64)
		var request MajordomoAdvertiserSelectV2Request
		request.AdvertiserId = organizationId
		//接口有调用频率限制
		time.Sleep(1 * time.Second)
		resp, err := GetToutiaoApiClient().MajordomoAdvertiserSelectV2Api.SetToken(accessToken).SetRequest(&request).Do()
		//接口频率控制流程
		if err != nil {
			if strings.Contains(err.Error(), "Too many requests") {
				g.Log().Info(ctx, fmt.Sprintf("-------------  执行 MajordomoAdvertiserSelectV2Api Too many requests  err:%v -------------------", err))
				time.Sleep(1 * time.Second)
				resp, err = GetToutiaoApiClient().MajordomoAdvertiserSelectV2Api.SetToken(accessToken).SetRequest(&request).Do()
			}
		}
		if err != nil || resp == nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- MajordomoAdvertiserSelectV2Api 刷新RefreshToken  organizationId:%v  err：%v,resp:%v  --------------------", organizationId, err, resp))
			continue
		}
		var strList []string
		for _, item := range resp.List {
			str := strconv.FormatInt(item.AdvertiserId, 10)
			if !libUtils.IsNullOrEmpty(str) {
				strList = append(strList, fmt.Sprintf("%v", str))
			}
		}
		g.Log().Info(ctx, fmt.Sprintf("------------- GetBusinessManagerRelations END 结束 setKey：%v , advertiserIdList:%+v --------------------", fmt.Sprintf("%s%v", OceanengineAdvertiserList, organizationId), strList))
		if len(strList) == 0 {
			continue
		}
		commonService.GetGoRedis().Del(ctx, fmt.Sprintf("%s%v", OceanengineAdvertiserList, organizationId))
		err = commonService.GetGoRedis().SAdd(ctx, fmt.Sprintf("%s%v", OceanengineAdvertiserList, organizationId), strList).Err()
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- 刷新RefreshToken  commonService.GetGoRedis().SAdd key:%v  err：%v --------------------", fmt.Sprintf("%s%v", OceanengineAdvertiserList, organizationId), err))
		}
	}
}

// GetReportCustomGetV30 open_api/v3.0/report/custom/get/
func GetReportCustomGetV30(ctx context.Context, advertiserId int64, startTime, endTime string, dimensionsTime string) (response *ReportCustomGetV30ResponseData, err error) {
	//advertiserId = 1792681725941769
	//
	//var accessToken = "dcbd5f8b8f3ddef9fded6d552282e66c793d2717"
	// 以上是test demo
	var accessToken = GetToutiaoAccessTokenByAdId(advertiserId)
	//g.Log().Info(ctx, fmt.Sprintf("------------- calcStatCost GetReportCustomGetV30 accessToken: %v   --------------------", accessToken))
	if len(accessToken) == 0 {
		return nil, errors.New("获取accessToken 失败！")
	}
	var dimensions = []string{dimensionsTime} //stat_time_hour  stat_time_day
	var metrics = []string{"stat_cost", "stat_attribution_micro_game_24h_amount", "stat_micro_game_0d_amount"}
	var filters []*ReportCustomGetV30FiltersInner
	filters = append(filters, &ReportCustomGetV30FiltersInner{
		Field:    "stat_cost",
		Operator: 4,
		Type:     3,
		Values:   []string{"0"},
	})
	var orderBy []*ReportCustomGetV30OrderByInner
	orderByType, _ := NewOrderByTypeFromValue("ASC")
	orderBy = append(orderBy, &ReportCustomGetV30OrderByInner{
		Field: "stat_cost",
		Type:  orderByType,
	})
	var request ApiOpenApiV30ReportCustomGetGetRequest
	request.AdvertiserId = &advertiserId
	request.Dimensions = dimensions
	request.Metrics = metrics
	request.StartTime = &startTime
	request.EndTime = &endTime
	request.Page = 1
	request.PageSize = 100 //最大100
	request.Filters = filters
	request.OrderBy = orderBy
	resp, err := GetToutiaoApiClient().ReportCustomGetV30Api.SetToken(accessToken).SetRequest(request).Do()

	return resp, err
}

func GetFiltersDayReportByMetrics(ctx context.Context, advertiserId int64, accessToken, startTime, endTime string, filters []*ReportCustomGetV30FiltersInner, orderBy []*ReportCustomGetV30OrderByInner) (response *ReportCustomGetV30ResponseData, err error) {
	metrics := GetMetrics()
	if len(accessToken) == 0 {
		return nil, errors.New("获取accessToken 失败！")
	}
	var dimensions = []string{"stat_time_day"} //stat_time_hour  stat_time_day
	var request ApiOpenApiV30ReportCustomGetGetRequest
	request.AdvertiserId = &advertiserId
	request.Dimensions = dimensions
	request.Metrics = metrics
	request.StartTime = &startTime
	request.EndTime = &endTime
	request.Page = 1
	request.PageSize = 100 //最大100
	request.Filters = filters
	request.OrderBy = orderBy
	resp, err := GetToutiaoApiClient().ReportCustomGetV30Api.SetToken(accessToken).SetRequest(request).Do()

	return resp, err
}

func GetFiltersHourReportByMetrics(ctx context.Context, advertiserId int64, accessToken, startTime, endTime string, filters []*ReportCustomGetV30FiltersInner, orderBy []*ReportCustomGetV30OrderByInner) (response *ReportCustomGetV30ResponseData, err error) {
	if len(accessToken) == 0 {
		return nil, errors.New(fmt.Sprintf("广告主ID：%v，获取accessToken 失败！)", advertiserId))
	}
	var metrics = []string{"stat_cost", "stat_pay_amount"}
	if filters == nil {
		filters = []*ReportCustomGetV30FiltersInner{}
	}
	if orderBy == nil {
		orderBy = []*ReportCustomGetV30OrderByInner{}
	}
	resp, err := GetToutiaoApiClient().ReportCustomGetV30Api.
		SetToken(accessToken).
		SetRequest(ApiOpenApiV30ReportCustomGetGetRequest{
			AdvertiserId: &advertiserId,
			Dimensions:   []string{"stat_time_hour"},
			Metrics:      metrics,
			StartTime:    &startTime,
			EndTime:      &endTime,
			Page:         1,
			PageSize:     100,
			Filters:      filters,
			OrderBy:      orderBy,
		}).Do()
	return resp, err
}

func GetMetrics() []string {
	t := reflect.TypeOf(AdAdvertiserAccountMetricsDataSeed{})
	fields := make([]string, t.NumField())
	// 遍历结构体的每个字段
	for i := 0; i < t.NumField(); i++ {
		// 获取字段
		field := t.Field(i)
		// 获取 ORM 标签
		ormTag := field.Tag.Get("orm")
		fields[i] = ormTag
	}
	return fields
}

func GetAgentAdvertiserSelectV2Api(ctx context.Context) {
	for _, agentId := range toutiaoService.AgentIds {
		var accessToken = commonService.GetGoRedis().Get(ctx, fmt.Sprintf("%s%v", OceanengineAccessToken, agentId)).Val()
		if libUtils.IsNullOrEmpty(accessToken) {
			continue
		}
		accessToken = strings.ReplaceAll(accessToken, `"`, "")
		var count int64 = 2000
		var cursor int64
		var advertiserId int64 = ************
		request := AgentAdvertiserSelectV2Request{
			AdvertiserId: &advertiserId,
			Count:        &count,
		}
		var advertiserIdList []string
		var total int64
		for {
			if cursor != 0 {
				request.Cursor = &cursor
			}
			resp, err := GetToutiaoApiClient().AgentAdvertiserSelectV2ApiService.AccessToken(accessToken).AgentAdvertiserSelectV2Request(request).Do()
			time.Sleep(20 * time.Millisecond)
			if err != nil || resp == nil {
				g.Log().Error(ctx, fmt.Sprintf("------------- AgentAdvertiserSelectV2Api err:%v -------------------", err))
				break
			}
			total = *resp.Data.CursorPageInfo.TotalNumber
			if resp.Data == nil || len(resp.Data.AdvertiserIds) == 0 {
				break
			}
			for _, item := range resp.Data.AdvertiserIds {
				advertiserIdList = append(advertiserIdList, fmt.Sprintf("%v", item))
			}
			if !*resp.Data.CursorPageInfo.HasMore {
				break
			}
			cursor = *resp.Data.CursorPageInfo.Cursor
		}
		if len(advertiserIdList) != int(total) {
			continue
		}
		if len(advertiserIdList) == 0 {
			continue
		}
		commonService.GetGoRedis().Del(ctx, fmt.Sprintf("%s%v", OceanengineAdvertiserList, agentId))
		err := commonService.GetGoRedis().SAdd(ctx, fmt.Sprintf("%s%v", OceanengineAdvertiserList, agentId), advertiserIdList).Err()
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- 刷新RefreshToken  commonService.GetGoRedis().SAdd key:%v  err：%v --------------------", fmt.Sprintf("%s%v", OceanengineAdvertiserList, agentId), err))
		}
	}
}
