// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/controller/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adDesignerMaterialReportController struct {
	systemController.BaseController
}

var AdDesignerMaterialReport = new(adDesignerMaterialReportController)

// List 列表
func (c *adDesignerMaterialReportController) List(ctx context.Context, req *ad.AdDesignerMaterialReportSearchReq) (res *ad.AdDesignerMaterialReportSearchRes, err error) {
	res = new(ad.AdDesignerMaterialReportSearchRes)
	res.AdDesignerMaterialSearchRes, err = service.AdDesignerMaterialReport().List(ctx, &req.AdDesignerMaterialReportSearchReq)
	return
}

// AdDesignerMaterialReport
func (c *adDesignerMaterialReportController) AdDesignerMaterialReport(ctx context.Context, req *ad.AdDesignerMaterialReportReq) (res *ad.AdDesignerMaterialReportRes, err error) {
	res = new(ad.AdDesignerMaterialReportRes)
	res.AdDesignerMaterialReportRes, err = service.AdDesignerMaterialReport().AdDesignerMaterialReport(ctx, req.AdDesignerMaterialReportReq)
	return
}

// Add 添加设计师素材数据统计
func (c *adDesignerMaterialReportController) Add(ctx context.Context, req *ad.AdDesignerMaterialReportAddReq) (res *ad.AdDesignerMaterialReportAddRes, err error) {
	err = service.AdDesignerMaterialReport().Add(ctx, req.AdDesignerMaterialReportAddReq)
	return
}

// Edit 修改设计师素材数据统计
func (c *adDesignerMaterialReportController) Edit(ctx context.Context, req *ad.AdDesignerMaterialReportEditReq) (res *ad.AdDesignerMaterialReportEditRes, err error) {
	err = service.AdDesignerMaterialReport().Edit(ctx, req.AdDesignerMaterialReportEditReq)
	return
}

// AdDesignerMaterialReportPullData
func (c *adDesignerMaterialReportController) AdDesignerMaterialReportPullData(ctx context.Context, req *ad.AdDesignerMaterialReportPullDataReq) (res *ad.AdDesignerMaterialReportPullDataRes, err error) {
	res, err = service.AdDesignerMaterialReport().PullData(ctx, req)
	return
}
