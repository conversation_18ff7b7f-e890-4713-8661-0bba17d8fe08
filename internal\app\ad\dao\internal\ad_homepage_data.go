// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-01-24 10:42:38
// 生成路径: internal/app/ad/dao/internal/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdHomepageDataDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdHomepageDataDao struct {
	table   string                // Table is the underlying table name of the DAO.
	group   string                // Group is the database configuration group name of current DAO.
	columns AdHomepageDataColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdHomepageDataColumns defines and stores column names for table ad_homepage_data.
type AdHomepageDataColumns struct {
	Id           string // ID
	Title        string // 标题
	Content      string // 内容
	ImageUrl     string // 图片链接
	LinkUrl      string // 跳转链接
	Platform     string // 广告平台
	Status       string // 状态：1启用 0禁用
	Sort         string // 排序
	ViewCount    string // 浏览次数
	ClickCount   string // 点击次数
	ConvertCount string // 转化次数
	CreatedAt    string // 创建时间
	UpdatedAt    string // 更新时间
	DeletedAt    string // 删除时间
}

var adHomepageDataColumns = AdHomepageDataColumns{
	Id:           "id",
	Title:        "title",
	Content:      "content",
	ImageUrl:     "image_url",
	LinkUrl:      "link_url",
	Platform:     "platform",
	Status:       "status",
	Sort:         "sort",
	ViewCount:    "view_count",
	ClickCount:   "click_count",
	ConvertCount: "convert_count",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewAdHomepageDataDao creates and returns a new DAO object for table data access.
func NewAdHomepageDataDao() *AdHomepageDataDao {
	return &AdHomepageDataDao{
		group:   "default",
		table:   "ad_homepage_data",
		columns: adHomepageDataColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdHomepageDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdHomepageDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdHomepageDataDao) Columns() AdHomepageDataColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdHomepageDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdHomepageDataDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdHomepageDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
