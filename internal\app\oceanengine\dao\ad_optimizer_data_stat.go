// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/dao/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao/internal"
)

// adOptimizerDataStatDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adOptimizerDataStatDao struct {
	*internal.AdOptimizerDataStatDao
}

var (
	// AdOptimizerDataStat is globally public accessible object for table tools_gen_table operations.
	AdOptimizerDataStat = adOptimizerDataStatDao{
		internal.NewAdOptimizerDataStatDao(),
	}
)

// Fill with you ideas below.
