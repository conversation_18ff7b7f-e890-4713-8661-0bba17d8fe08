// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-19 10:47:09
// 生成路径: internal/app/oceanengine/logic/ad_promotion_metrics_data.go
// 生成人：cyao
// desc:广告账户下的广告的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/entity"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdPromotionMetricsData(New())
}

func New() service.IAdPromotionMetricsData {
	return &sAdPromotionMetricsData{}
}

type sAdPromotionMetricsData struct{}

func (s *sAdPromotionMetricsData) AdPromotionReportDataSearch(ctx context.Context, req *model.AdPromotionReportDataSearch) (listRes *model.AdPromotionReportDataSearchRes, err error) {
	listRes = new(model.AdPromotionReportDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPromotion.Ctx(ctx).As("a").
			LeftJoin("ad_project", "c", "a.project_id = c.project_id").
			LeftJoin("ad_advertiser_account", "d", "a.advertiser_id = d.advertiser_id").
			LeftJoin("sys_user u", "a.user_id = u.id")
		//if req.StartTime == "" {
		//	req.StartTime = libUtils.GetNowDate()
		//}
		//if req.EndTime == "" {
		//	req.EndTime = libUtils.GetNowDate()
		//}
		m = m.LeftJoin("ad_promotion_metrics_data", "b",
			fmt.Sprintf("a.promotion_id = b.promotion_id AND b.create_date >= '%s' AND b.create_date <= '%s'", req.StartTime, req.EndTime))
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().UserId, req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a."+dao.AdPromotion.Columns().UserId, userIds)
			}
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.PromotionIds) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().PromotionId, req.PromotionIds)
		}
		if len(req.PromotionNames) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().PromotionName, req.PromotionNames)
		}
		if len(req.ProjectIds) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().ProjectId, req.ProjectIds)
		}
		if len(req.Companies) > 0 {
			m = m.WhereIn("d."+dao.AdAdvertiserAccount.Columns().AdvertiserCompany, req.Companies)
		}
		if req.DeliveryMode != "" {
			m = m.Where("c."+dao.AdProject.Columns().DeliveryMode, req.DeliveryMode)
		}
		if req.AdType != "" {
			m = m.Where("c."+dao.AdProject.Columns().AdType, req.AdType)
		}
		if req.Status != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().Status, req.Status)
		}
		if req.AdvertiserId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ProjectId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().ProjectId, req.ProjectId)
		}
		if req.Keyword != "" {
			m = m.Where("a.promotion_id like ? or a.promotion_name like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		group := "a.promotion_id"
		order := "ANY_VALUE(a.promotion_create_time) desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(group).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields := make([]string, 0)
		// 公共参数
		fields = append(fields, "ANY_VALUE(a.id) as id")
		fields = append(fields, "ANY_VALUE(a.status) as status")
		fields = append(fields, "ANY_VALUE(a.opt_status) as optStatus")
		fields = append(fields, "ANY_VALUE(a.promotion_name) as promotionName")
		fields = append(fields, "ANY_VALUE(a.promotion_id) as promotionId")
		fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		fields = append(fields, "ANY_VALUE(a.budget) as budget")
		fields = append(fields, "ANY_VALUE(a.cpa_bid) as cpaBid")
		fields = append(fields, "ANY_VALUE(a.deep_cpa_bid) as deepCpaBid")
		fields = append(fields, "ANY_VALUE(d.advertiser_company) as advertiserCompany")
		fields = append(fields, "ANY_VALUE(a.promotion_create_time) as promotionCreateTime")
		fields = append(fields, "ANY_VALUE(a.advertiser_id) as advertiserId")

		// todo 根据自定义列筛选字段
		fields = append(fields, "ROUND(SUM(b.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(b.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(b.stat_pay_amount)/SUM(b.stat_cost)*100,2) as payAmountRoi")
		fields = append(fields, "SUM(b.show_cnt) as showCnt")
		fields = append(fields, "ROUND(SUM(b.stat_cost)/SUM(b.show_cnt)*1000,2) as cpmPlatform")
		fields = append(fields, "SUM(b.click_cnt) as clickCnt")
		fields = append(fields, "ROUND(SUM(b.click_cnt)/SUM(b.show_cnt)*100,2) as ctr")
		fields = append(fields, "SUM(b.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(b.stat_cost)/SUM(b.convert_cnt),2) as conversionCost")
		var res []*model.AdPromotionReportDataRes
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(group).
			Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPromotionReportDataRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdPromotionReportDataRes{
				Id:                                    v.Id,
				PromotionId:                           v.PromotionId,
				PromotionName:                         v.PromotionName,
				ProjectId:                             v.ProjectId,
				AdvertiserId:                          v.AdvertiserId,
				StatPayAmount:                         v.StatPayAmount,
				PayAmountRoi:                          v.PayAmountRoi,
				Status:                                v.Status,
				OptStatus:                             v.OptStatus,
				Budget:                                v.Budget,
				BudgetMode:                            v.BudgetMode,
				CpaBid:                                v.CpaBid,
				DeepCpaBid:                            v.DeepCpaBid,
				AdvertiserCompany:                     v.AdvertiserCompany,
				PromotionCreateTime:                   v.PromotionCreateTime,
				UserName:                              v.UserName,
				StatCost:                              v.StatCost,
				ShowCnt:                               v.ShowCnt,
				CpmPlatform:                           v.CpmPlatform,
				ClickCnt:                              v.ClickCnt,
				Ctr:                                   v.Ctr,
				CpcPlatform:                           v.CpcPlatform,
				ConvertCnt:                            v.ConvertCnt,
				ConversionCost:                        v.ConversionCost,
				ConversionRate:                        v.ConversionRate,
				DeepConvertCnt:                        v.DeepConvertCnt,
				DeepConvertCost:                       v.DeepConvertCost,
				DeepConvertRate:                       v.DeepConvertRate,
				AttributionConvertCnt:                 v.AttributionConvertCnt,
				AttributionConvertCost:                v.AttributionConvertCost,
				AttributionDeepConvertCnt:             v.AttributionDeepConvertCnt,
				AttributionDeepConvertCost:            v.AttributionDeepConvertCost,
				PreConvertCount:                       v.PreConvertCount,
				PreConvertCost:                        v.PreConvertCost,
				PreConvertRate:                        v.PreConvertRate,
				ClickStartCnt:                         v.ClickStartCnt,
				ClickStartCost:                        v.ClickStartCost,
				ClickStartRate:                        v.ClickStartRate,
				DownloadFinishCnt:                     v.DownloadFinishCnt,
				DownloadFinishCost:                    v.DownloadFinishCost,
				DownloadFinishRate:                    v.DownloadFinishRate,
				InstallFinishCnt:                      v.InstallFinishCnt,
				InstallFinishCost:                     v.InstallFinishCost,
				InstallFinishRate:                     v.InstallFinishRate,
				Active:                                v.Active,
				ActiveCost:                            v.ActiveCost,
				ActiveRate:                            v.ActiveRate,
				ActiveRegisterCost:                    v.ActiveRegisterCost,
				ActiveRegisterRate:                    v.ActiveRegisterRate,
				GameAddiction:                         v.GameAddiction,
				GameAddictionCost:                     v.GameAddictionCost,
				GameAddictionRate:                     v.GameAddictionRate,
				AttributionNextDayOpenCnt:             v.AttributionNextDayOpenCnt,
				AttributionNextDayOpenCost:            v.AttributionNextDayOpenCost,
				AttributionNextDayOpenRate:            v.AttributionNextDayOpenRate,
				NextDayOpen:                           v.NextDayOpen,
				ActivePay:                             v.ActivePay,
				ActivePayCost:                         v.ActivePayCost,
				ActivePayRate:                         v.ActivePayRate,
				GamePayCount:                          v.GamePayCount,
				GamePayCost:                           v.GamePayCost,
				AttributionGamePay7DCount:             v.AttributionGamePay7DCount,
				AttributionGamePay7DCost:              v.AttributionGamePay7DCost,
				AttributionActivePay7DPerCount:        v.AttributionActivePay7DPerCount,
				InAppUv:                               v.InAppUv,
				InAppDetailUv:                         v.InAppDetailUv,
				InAppCart:                             v.InAppCart,
				InAppPay:                              v.InAppPay,
				InAppOrder:                            v.InAppOrder,
				AttributionGameInAppLtv1Day:           v.AttributionGameInAppLtv1Day,
				AttributionGameInAppLtv2Days:          v.AttributionGameInAppLtv2Days,
				AttributionGameInAppLtv3Days:          v.AttributionGameInAppLtv3Days,
				AttributionGameInAppLtv4Days:          v.AttributionGameInAppLtv4Days,
				AttributionGameInAppLtv5Days:          v.AttributionGameInAppLtv5Days,
				AttributionGameInAppLtv6Days:          v.AttributionGameInAppLtv6Days,
				AttributionGameInAppLtv7Days:          v.AttributionGameInAppLtv7Days,
				AttributionGameInAppLtv8Days:          v.AttributionGameInAppLtv8Days,
				AttributionGameInAppRoi1Day:           v.AttributionGameInAppRoi1Day,
				AttributionGameInAppRoi2Days:          v.AttributionGameInAppRoi2Days,
				AttributionGameInAppRoi3Days:          v.AttributionGameInAppRoi3Days,
				AttributionGameInAppRoi4Days:          v.AttributionGameInAppRoi4Days,
				AttributionGameInAppRoi5Days:          v.AttributionGameInAppRoi5Days,
				AttributionGameInAppRoi6Days:          v.AttributionGameInAppRoi6Days,
				AttributionGameInAppRoi7Days:          v.AttributionGameInAppRoi7Days,
				AttributionGameInAppRoi8Days:          v.AttributionGameInAppRoi8Days,
				AttributionDayActivePayCount:          v.AttributionDayActivePayCount,
				AttributionActivePayIntraOneDayCount:  v.AttributionActivePayIntraOneDayCount,
				AttributionActivePayIntraOneDayCost:   v.AttributionActivePayIntraOneDayCost,
				AttributionActivePayIntraOneDayRate:   v.AttributionActivePayIntraOneDayRate,
				AttributionActivePayIntraOneDayAmount: v.AttributionActivePayIntraOneDayAmount,
				AttributionActivePayIntraOneDayRoi:    v.AttributionActivePayIntraOneDayRoi,
				AttributionRetention2DCnt:             v.AttributionRetention2DCnt,
				AttributionRetention2DCost:            v.AttributionRetention2DCost,
				AttributionRetention2DRate:            v.AttributionRetention2DRate,
				AttributionRetention3DCnt:             v.AttributionRetention3DCnt,
				AttributionRetention3DCost:            v.AttributionRetention3DCost,
				AttributionRetention3DRate:            v.AttributionRetention3DRate,
				AttributionRetention4DCnt:             v.AttributionRetention4DCnt,
				AttributionRetention4DCost:            v.AttributionRetention4DCost,
				AttributionRetention4DRate:            v.AttributionRetention4DRate,
				AttributionRetention5DCnt:             v.AttributionRetention5DCnt,
				AttributionRetention5DCost:            v.AttributionRetention5DCost,
				AttributionRetention5DRate:            v.AttributionRetention5DRate,
				AttributionRetention6DCnt:             v.AttributionRetention6DCnt,
				AttributionRetention6DCost:            v.AttributionRetention6DCost,
				AttributionRetention6DRate:            v.AttributionRetention6DRate,
				AttributionRetention7DCnt:             v.AttributionRetention7DCnt,
				AttributionRetention7DCost:            v.AttributionRetention7DCost,
				AttributionRetention7DRate:            v.AttributionRetention7DRate,
				AttributionRetention7DSumCnt:          v.AttributionRetention7DSumCnt,
				AttributionRetention7DTotalCost:       v.AttributionRetention7DTotalCost,
				TotalPlay:                             v.TotalPlay,
				ValidPlay:                             v.ValidPlay,
				ValidPlayCost:                         v.ValidPlayCost,
				ValidPlayRate:                         v.ValidPlayRate,
				Play25FeedBreak:                       v.Play25FeedBreak,
				Play50FeedBreak:                       v.Play50FeedBreak,
				Play75FeedBreak:                       v.Play75FeedBreak,
				Play99FeedBreak:                       v.Play99FeedBreak,
				AveragePlayTimePerPlay:                v.AveragePlayTimePerPlay,
				PlayOverRate:                          v.PlayOverRate,
				WifiPlayRate:                          v.WifiPlayRate,
				CardShow:                              v.CardShow,
				DyLike:                                v.DyLike,
				DyComment:                             v.DyComment,
				DyShare:                               v.DyShare,
				IesChallengeClick:                     v.IesChallengeClick,
				IesMusicClick:                         v.IesMusicClick,
				LocationClick:                         v.LocationClick,
				CustomerEffective:                     v.CustomerEffective,
				Wechat:                                v.Wechat,
				AttributionMicroGame0DLtv:             v.AttributionMicroGame0DLtv,
				AttributionMicroGame3DLtv:             v.AttributionMicroGame3DLtv,
				AttributionMicroGame7DLtv:             v.AttributionMicroGame7DLtv,
				AttributionMicroGame0DRoi:             v.AttributionMicroGame0DRoi,
				AttributionMicroGame3DRoi:             v.AttributionMicroGame3DRoi,
				AttributionMicroGame7DRoi:             v.AttributionMicroGame7DRoi,
				TotalAmount:                           v.TotalAmount,
				TotalAdUp:                             v.TotalAdUp,
				Register:                              v.Register,
			}
		}
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
	})
	return
}

func (s *sAdPromotionMetricsData) AdPromotionReportDataSearch2(ctx context.Context, req *model.AdPromotionReportDataSearch2) (listRes *model.AdPromotionReportDataSearchRes2, err error) {
	listRes = new(model.AdPromotionReportDataSearchRes2)
	//if req.StartTime == "" {
	//	req.StartTime = libUtils.GetNowDate()
	//}
	//if req.EndTime == "" {
	//	req.EndTime = libUtils.GetNowDate()
	//}
	err = g.Try(ctx, func(ctx context.Context) {
		//ad_promotion_metrics_data
		m := dao.AdPromotionMetricsData.Ctx(ctx).As("b").LeftJoin("ad_promotion", "a", "a.promotion_id = b.promotion_id").
			LeftJoin("ad_project", "c", "a.project_id = c.project_id").
			LeftJoin("ad_advertiser_account", "d", "a.advertiser_id = d.advertiser_id").
			LeftJoin("sys_user u", "a.user_id = u.id")

		if len(req.StartTime) > 0 {
			m = m.Where("b.create_date >= ?", req.StartTime)
		}
		if len(req.EndTime) > 0 {
			m = m.Where("b.create_date <= ?", req.EndTime)
		}

		//m = m.LeftJoin("ad_promotion_metrics_data", "b",
		//	fmt.Sprintf("a.promotion_id = b.promotion_id AND b.create_date >= '%s' AND b.create_date <= '%s'", req.StartTime, req.EndTime))
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().UserId, req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a."+dao.AdPromotion.Columns().UserId, userIds)
			}
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.PromotionIds) > 0 {
			m = m.WhereIn("b."+dao.AdPromotion.Columns().PromotionId, req.PromotionIds)
		}
		if len(req.PromotionNames) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().PromotionName, req.PromotionNames)
		}
		if len(req.ProjectIds) > 0 {
			m = m.WhereIn("b."+dao.AdPromotion.Columns().ProjectId, req.ProjectIds)
		}
		if len(req.LandingType) > 0 {
			m = m.WhereIn("c."+dao.AdProject.Columns().LandingType, req.LandingType)
		}
		if len(req.Companies) > 0 {
			m = m.WhereIn("d."+dao.AdAdvertiserAccount.Columns().AdvertiserCompany, req.Companies)
		}
		if req.DeliveryMode != "" {
			m = m.Where("c."+dao.AdProject.Columns().DeliveryMode, req.DeliveryMode)
		}
		if req.AdType != "" {
			m = m.Where("c."+dao.AdProject.Columns().AdType, req.AdType)
		}
		//if req.Status != "" {
		//	m = m.Where("a."+dao.AdPromotion.Columns().Status, req.Status)
		//}
		if req.AdvertiserId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ProjectId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().ProjectId, req.ProjectId)
		}
		if req.Keyword != "" {
			m = m.Where("a.promotion_id like ? or a.promotion_name like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		//group := "a.promotion_id"
		order := "b.create_date desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields := make([]string, 0)
		// 公共参数
		fields = append(fields, "ANY_VALUE(b.create_date) as create_date")

		fields = append(fields, "ANY_VALUE(a.promotion_name) as promotionName")
		fields = append(fields, "ANY_VALUE(b.promotion_id) as promotionId")
		//fields = append(fields, "ANY_VALUE(a.advertiser_id) as advertiser_id")
		//fields = append(fields, "ANY_VALUE(d.advertiser_nick) as advertiserName")
		fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		fields = append(fields, "ANY_VALUE(d.advertiser_company) as advertiserCompany")

		// todo 根据自定义列筛选字段
		fields = append(fields, "ROUND(SUM(b.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(b.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(b.stat_pay_amount)/SUM(b.stat_cost)*100,2) as payAmountRoi")
		fields = append(fields, "SUM(b.show_cnt) as showCnt")
		fields = append(fields, "ROUND(SUM(b.stat_cost)/SUM(b.show_cnt)*1000,2) as cpmPlatform")
		fields = append(fields, "SUM(b.click_cnt) as clickCnt")
		fields = append(fields, "ROUND(SUM(b.click_cnt)/SUM(b.show_cnt)*100,2) as ctr")
		fields = append(fields, "SUM(b.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(b.stat_cost)/SUM(b.convert_cnt),2) as conversionCost")
		fields = append(fields, "SUM(b.active) as active")
		fields = append(fields, "ROUND(SUM(b.stat_cost)/SUM(b.active),2) as  activeCost")
		fields = append(fields, "ROUND(SUM(b.convert_cnt)/SUM(b.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "ROUND(SUM(b.active)/SUM(b.click_cnt),2) as activeRate")
		fields = append(fields, "SUM(b.attribution_game_in_app_ltv_1day) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(b.attribution_game_in_app_ltv_1day)/SUM(b.stat_cost),2) as attributionGameInAppRoi1Day")
		fields = append(fields, "SUM(b.attribution_micro_game_0d_ltv) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(b.attribution_micro_game_0d_ltv)/SUM(b.stat_cost),2) as attributionMicroGame0DRoi")
		fields = append(fields, "SUM(b.active_pay) as activePay")
		fields = append(fields, "ROUND(SUM(b.active_pay)/SUM(b.active),4)*100 as activePayRate")
		fields = append(fields, "SUM(b.game_pay_count) as gamePayCount")
		// 付费率
		fields = append(fields, "ROUND(SUM(b.game_pay_count)/SUM(b.active),4)*100 as payRate")
		var res []*model.AdPromotionReportDataRes2
		err = m.Fields(`  b.create_date AS create_date,
 			a.advertiser_id AS advertiser_id,
			c.landing_type as landingType,
 			u.user_name AS userName,
 			a.promotion_name as promotionName ,
			b.promotion_id as promotionId,
 			d.advertiser_company AS advertiserCompany,
 			d.advertiser_nick AS advertiserName,
 			ROUND(b.stat_cost, 2) AS statCost,
 			ROUND(b.stat_pay_amount, 2) AS statPayAmount,
 			ROUND(b.stat_pay_amount / b.stat_cost * 100, 2) AS payAmountRoi,
 			b.show_cnt AS showCnt,
 			ROUND(b.stat_cost / b.show_cnt * 1000, 2) AS cpmPlatform,
 			b.click_cnt AS clickCnt,
 			ROUND(b.click_cnt / b.show_cnt * 100, 2) AS ctr,
 			b.convert_cnt AS convertCnt,
 			ROUND(b.stat_cost / b.convert_cnt, 2) AS conversionCost,
			ROUND(b.convert_cnt / b.click_cnt*100,2) as conversionRate,
 			b.active AS active,
 			b.stat_cost/ b.active AS activeCost,
 			ROUND(b.active /b.click_cnt , 2) AS activeRate,
 			b.attribution_game_in_app_ltv_1day AS attributionGameInAppLtv1Day,
 			ROUND(b.attribution_game_in_app_ltv_1day / b.stat_cost, 2) AS attributionGameInAppRoi1Day,
 			b.attribution_micro_game_0d_ltv AS attributionMicroGame0DLtv,
 			ROUND(b.attribution_micro_game_0d_ltv / b.stat_cost, 2) AS attributionMicroGame0DRoi,
 			b.active_pay AS activePay,
 			ROUND(b.active_pay / b.active * 100, 4) AS activePayRate,
 			b.game_pay_count AS gamePayCount,
 			ROUND(b.game_pay_count / b.active * 100, 4) AS payRate`).
			Page(req.PageNum, req.PageSize).
			//Group(group).
			Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPromotionReportDataRes2, 0)
		for _, v := range res {
			if len(v.PromotionId) == 0 || v.PromotionId == "0" {
				continue
			}
			listRes.List = append(listRes.List, &model.AdPromotionReportDataRes2{
				CpmPlatform:                 v.CpmPlatform,
				LandingType:                 v.LandingType,
				CreateDate:                  v.CreateDate,
				PromotionId:                 v.PromotionId,
				PromotionName:               v.PromotionName,
				StatPayAmount:               v.StatPayAmount,
				PayAmountRoi:                v.PayAmountRoi,
				AdvertiserCompany:           v.AdvertiserCompany,
				AdvertiserName:              v.AdvertiserName,
				UserName:                    v.UserName,
				StatCost:                    v.StatCost,
				ShowCnt:                     v.ShowCnt,
				ClickCnt:                    v.ClickCnt,
				Ctr:                         v.Ctr,
				ConvertCnt:                  v.ConvertCnt,
				ConversionCost:              v.ConversionCost,
				ConversionRate:              v.ConversionRate,
				Active:                      v.Active,
				ActiveCost:                  v.ActiveCost,
				ActiveRate:                  v.ActiveRate,
				ActivePay:                   v.ActivePay,
				ActivePayRate:               v.ActivePayRate,
				GamePayCount:                v.GamePayCount,
				AttributionGameInAppLtv1Day: v.AttributionGameInAppLtv1Day,
				AttributionGameInAppRoi1Day: v.AttributionGameInAppRoi1Day,
				AttributionMicroGame0DLtv:   v.AttributionMicroGame0DLtv,
				AttributionMicroGame0DRoi:   v.AttributionMicroGame0DRoi,
			})
		}
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
	})
	return
}

func (s *sAdPromotionMetricsData) AdPromotionAccountReportDataSearch(ctx context.Context, req *model.AdPromotionAccountReportDataSearch) (listRes *model.AdPromotionAccountReportDataSearchRes, err error) {
	listRes = new(model.AdPromotionAccountReportDataSearchRes)
	//if req.StartTime == "" {
	//	req.StartTime = libUtils.GetNowDate()
	//}
	//if req.EndTime == "" {
	//	req.EndTime = libUtils.GetNowDate()
	//}
	err = g.Try(ctx, func(ctx context.Context) {
		//ad_promotion_metrics_data
		m := dao.AdPromotionMetricsData.Ctx(ctx).As("b").LeftJoin("ad_promotion", "a", "a.promotion_id = b.promotion_id").
			LeftJoin("ad_project", "c", "a.project_id = c.project_id").
			LeftJoin("ad_advertiser_account", "d", "a.advertiser_id = d.advertiser_id").
			LeftJoin("sys_user u", "a.user_id = u.id")

		if len(req.StartTime) > 0 {
			m = m.Where("b.create_date >= ?", req.StartTime)
		}
		if len(req.EndTime) > 0 {
			m = m.Where("b.create_date <= ?", req.EndTime)
		}

		//m = m.LeftJoin("ad_promotion_metrics_data", "b",
		//	fmt.Sprintf("a.promotion_id = b.promotion_id AND b.create_date >= '%s' AND b.create_date <= '%s'", req.StartTime, req.EndTime))
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().UserId, req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a."+dao.AdPromotion.Columns().UserId, userIds)
			}
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.PromotionIds) > 0 {
			m = m.WhereIn("b."+dao.AdPromotion.Columns().PromotionId, req.PromotionIds)
		}
		if len(req.PromotionNames) > 0 {
			m = m.WhereIn("a."+dao.AdPromotion.Columns().PromotionName, req.PromotionNames)
		}
		if len(req.ProjectIds) > 0 {
			m = m.WhereIn("b."+dao.AdPromotion.Columns().ProjectId, req.ProjectIds)
		}
		if len(req.Companies) > 0 {
			m = m.WhereIn("d."+dao.AdAdvertiserAccount.Columns().AdvertiserCompany, req.Companies)
		}
		if len(req.AdvertiserId) > 0 {
			m = m.WhereIn("d."+dao.AdAdvertiserAccount.Columns().AdvertiserId, req.AdvertiserId)
		}

		if len(req.AccountName) > 0 {
			m = m.WhereIn("d."+dao.AdAdvertiserAccount.Columns().AdvertiserNick, req.AccountName)
		}

		if req.DeliveryMode != "" {
			m = m.Where("c."+dao.AdProject.Columns().DeliveryMode, req.DeliveryMode)
		}
		if req.AdType != "" {
			m = m.Where("c."+dao.AdProject.Columns().AdType, req.AdType)
		}
		//if req.Status != "" {
		//	m = m.Where("a."+dao.AdPromotion.Columns().Status, req.Status)
		//}
		if req.AdvertiserId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ProjectId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().ProjectId, req.ProjectId)
		}
		if req.Keyword != "" {
			m = m.Where("a.promotion_id like ? or a.promotion_name like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		group := "b.advertiser_id,b.create_date"
		order := "b.create_date desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(group).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields := make([]string, 0)
		fields2 := make([]string, 0)
		// 公共参数

		fields = append(fields, "b.advertiser_id as advertiserId")
		fields = append(fields, "b.create_date as createDate")
		fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		fields = append(fields, "ANY_VALUE(d.advertiser_company) as advertiserCompany")
		fields = append(fields, "ANY_VALUE(d.advertiser_nick) as advertiserName")
		fields2 = append(fields2, "Count(DISTINCT a.promotion_id) as totalAdNums")
		// todo 根据自定义列筛选字段
		fields2 = append(fields2, "ROUND(SUM(b.stat_cost),2) as statCost")
		fields2 = append(fields2, "ROUND(SUM(b.stat_pay_amount),2) as statPayAmount")
		fields2 = append(fields2, "ROUND(SUM(b.stat_pay_amount)/SUM(b.stat_cost)*100,2) as payAmountRoi")
		fields2 = append(fields2, "SUM(b.show_cnt) as showCnt")
		fields2 = append(fields2, "ROUND(SUM(b.stat_cost)/SUM(b.show_cnt)*1000,2) as cpmPlatform")
		fields2 = append(fields2, "SUM(b.click_cnt) as clickCnt")
		fields2 = append(fields2, "ROUND(SUM(b.click_cnt)/SUM(b.show_cnt)*100,2) as ctr")
		fields2 = append(fields2, "SUM(b.convert_cnt) as convertCnt")
		fields2 = append(fields2, "ROUND(SUM(b.stat_cost)/SUM(b.convert_cnt),2) as conversionCost")
		fields2 = append(fields2, "SUM(b.active) as active")
		fields2 = append(fields2, "ROUND(SUM(b.stat_cost)/SUM(b.active),2) as  activeCost")
		fields2 = append(fields2, "ROUND(SUM(b.active)/SUM(b.click_cnt),2) as activeRate")
		fields2 = append(fields2, "SUM(b.attribution_game_in_app_ltv_1day) as attributionGameInAppLtv1Day")
		fields2 = append(fields2, "ROUND(SUM(b.attribution_game_in_app_ltv_1day)/SUM(b.stat_cost),2) as attributionGameInAppRoi1Day")
		fields2 = append(fields2, "SUM(b.attribution_micro_game_0d_ltv) as attributionMicroGame0DLtv")
		fields2 = append(fields2, "ROUND(SUM(b.attribution_micro_game_0d_ltv)/SUM(b.stat_cost),2) as attributionMicroGame0DRoi")
		fields2 = append(fields2, "SUM(b.active_pay) as activePay")
		fields2 = append(fields2, "ROUND(SUM(b.active_pay)/SUM(b.active),4)*100 as activePayRate")
		fields2 = append(fields2, "SUM(b.game_pay_count) as gamePayCount")
		// 付费率
		fields2 = append(fields2, "ROUND(SUM(b.game_pay_count)/SUM(b.active),4)*100 as payRate")

		var res []*model.AdPromotionAccountReportDataRes
		fields = append(fields, fields2...)
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(group).
			Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPromotionAccountReportDataRes, 0)
		for _, v := range res {
			if len(v.AdvertiserId) == 0 || v.AdvertiserId == "0" {
				continue
			}
			listRes.List = append(listRes.List, &model.AdPromotionAccountReportDataRes{
				CpmPlatform:                 v.CpmPlatform,
				AdvertiserName:              v.AdvertiserName,
				CreateDate:                  v.CreateDate,
				AdvertiserId:                v.AdvertiserId,
				TotalAdNums:                 v.TotalAdNums,
				PromotionId:                 v.PromotionId,
				PromotionName:               v.PromotionName,
				StatPayAmount:               v.StatPayAmount,
				PayAmountRoi:                v.PayAmountRoi,
				AdvertiserCompany:           v.AdvertiserCompany,
				UserName:                    v.UserName,
				StatCost:                    v.StatCost,
				ShowCnt:                     v.ShowCnt,
				ClickCnt:                    v.ClickCnt,
				Ctr:                         v.Ctr,
				ConvertCnt:                  v.ConvertCnt,
				ConversionCost:              v.ConversionCost,
				ConversionRate:              v.ConversionRate,
				Active:                      v.Active,
				ActiveCost:                  v.ActiveCost,
				ActiveRate:                  v.ActiveRate,
				ActivePay:                   v.ActivePay,
				ActivePayRate:               v.ActivePayRate,
				GamePayCount:                v.GamePayCount,
				AttributionGameInAppLtv1Day: v.AttributionGameInAppLtv1Day,
				AttributionGameInAppRoi1Day: v.AttributionGameInAppRoi1Day,
				AttributionMicroGame0DLtv:   v.AttributionMicroGame0DLtv,
				AttributionMicroGame0DRoi:   v.AttributionMicroGame0DRoi,
			})
		}
		err1 := m.Fields(fields2).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
	})
	return
}

// AdPromotionMaterialStatistics
func (s *sAdPromotionMetricsData) AdPromotionMaterialStatistics(ctx context.Context, req *model.AdPromotionMaterialStatisticsReq) (listRes *model.AdPromotionMaterialReportDataSearchRes, err error) {
	listRes = new(model.AdPromotionMaterialReportDataSearchRes)
	//if req.StartTime == "" {
	//	req.StartTime = libUtils.GetNowDate()
	//}
	//if req.EndTime == "" {
	//	req.EndTime = libUtils.GetNowDate()
	//}
	err = g.Try(ctx, func(ctx context.Context) {

		//ad_promotion_metrics_data
		m := dao.AdPromotionMetricsData.Ctx(ctx).As("b").LeftJoin("ad_promotion", "a", "a.promotion_id = b.promotion_id").
			InnerJoin("ad_material_promotion", "amp", "b.promotion_id = amp.promotion_id").
			InnerJoin("ad_material", "am", "amp.material_id = am.material_id").
			LeftJoin("sys_user u", "am.user_id = u.id")

		if len(req.StartTime) > 0 {
			m = m.Where("b.create_date >= ?", req.StartTime)
		}
		if len(req.EndTime) > 0 {
			m = m.Where("b.create_date <= ?", req.EndTime)
		}
		if req.FileId > 0 {
			m = m.Where("am.file_id = ?", req.FileId)
		}
		if len(req.MaterialIds) > 0 {
			m = m.WhereIn("am.material_id", req.MaterialIds)
		}

		//m = m.LeftJoin("ad_promotion_metrics_data", "b",
		//	fmt.Sprintf("a.promotion_id = b.promotion_id AND b.create_date >= '%s' AND b.create_date <= '%s'", req.StartTime, req.EndTime))
		if len(req.UserIds) > 0 {
			m = m.WhereIn("am.user_id", req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("am.user_id", userIds)
			}
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.ProjectIds) > 0 {
			m = m.WhereIn("b."+dao.AdPromotion.Columns().ProjectId, req.ProjectIds)
		}
		if len(req.Companies) > 0 {
			m = m.WhereIn("d."+dao.AdAdvertiserAccount.Columns().AdvertiserCompany, req.Companies)
		}
		if req.AdvertiserId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ProjectId != "" {
			m = m.Where("a."+dao.AdPromotion.Columns().ProjectId, req.ProjectId)
		}
		//if req.Keyword != "" {
		//	m = m.Where("a.promotion_id like ? or a.promotion_name like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		//}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}

		group := "b.create_date, am.material_id"
		order := "b.create_date desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(group).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields := make([]string, 0)
		fields2 := make([]string, 0)

		fields = append(fields, " b.create_date as create_date")
		fields = append(fields, " am.material_id as materialId")
		fields = append(fields, " am.material_name as material_name")
		fields = append(fields, " am.file_id  as material_dir_id")
		fields = append(fields, " am.user_id as designer_id")
		fields = append(fields, " COUNT(DISTINCT amp.promotion_id) AS promotion_count")

		fields2 = append(fields2, ` SUM(b.stat_cost) AS statCost,
 		 SUM(b.show_cnt) AS impressions,
 		 ROUND(SUM(b.stat_cost) / NULLIF(SUM(b.show_cnt), 0) * 1000, 2) AS cpmPlatform,
 		 SUM(b.click_cnt) AS clicks,
 		 ROUND(SUM(b.click_cnt) / NULLIF(SUM(b.show_cnt), 0), 4) AS ctr,
 		 SUM(b.convert_cnt) AS conversions,
 		 ROUND(SUM(b.stat_cost) / NULLIF(SUM(b.convert_cnt), 0), 2) AS cpa,
 		 ROUND(SUM(b.convert_cnt) / NULLIF(SUM(b.click_cnt), 0), 4) AS conversionRate,
 		 SUM(b.active) AS actives,
 		 ROUND(SUM(b.stat_cost) / NULLIF(SUM(b.active), 0), 2) AS activeCost,
 		 ROUND(SUM(b.active) / NULLIF(SUM(b.click_cnt), 0), 4) AS activeRate,
 		 SUM(b.active_pay) AS firstPayCount,
 		 ROUND(SUM(b.active_pay) / NULLIF(SUM(b.active), 0), 4) AS firstPayRate`)

		var res []*model.AdPromotionMaterialReportDataRes
		fields = append(fields, fields2...)
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(group).
			Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPromotionMaterialReportDataRes, 0)
		mIds := make([]int, 0)
		for _, v := range res {
			if v.MaterialID > 0 {
				mIds = append(mIds, v.MaterialID)
				listRes.List = append(listRes.List, &model.AdPromotionMaterialReportDataRes{
					CreateDate:   v.CreateDate,
					MaterialName: v.MaterialName,
					MaterialID:   v.MaterialID,
					//MaterialDirID:  v.MaterialDirID,
					DesignerID:     v.DesignerID,
					PromotionCount: v.PromotionCount,
					Cost:           v.Cost,
					Impressions:    v.Impressions,
					CpmPlatform:    v.CpmPlatform,
					Clicks:         v.Clicks,
					Ctr:            v.Ctr,
					Conversions:    v.Conversions,
					Cpa:            v.Cpa,
					ConversionRate: v.ConversionRate,
					Actives:        v.Actives,
					ActiveCost:     v.ActiveCost,
					ActiveRate:     v.ActiveRate,
					FirstPayCount:  v.FirstPayCount,
					FirstPayRate:   v.FirstPayRate,
				})
			}
		}

		for _, item := range listRes.List {
			MaterialInfo, err := adService.AdMaterial().GetByMaterialId(ctx, item.MaterialID)
			if err == nil {
				item.Designer = MaterialInfo.DesignUser
				item.MaterialDir = MaterialInfo.FilePath
			}
		}

		err1 := m.Fields(fields2).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
	})
	return

}

// GetProjectMetricsByPlanTask 根据执行计划获取指标相关数据
func (s *sAdPromotionMetricsData) GetProjectMetricsByPlanTask(ctx context.Context, ids, metricsName []string, TimeScope int) (list []*model.MetricsByPlanTaskRes) {
	startTime := libUtils.PlusDays(libUtils.GetNowDate(), -int64(TimeScope))
	endTime := libUtils.GetNowDate()

	m := dao.AdPromotion.Ctx(ctx).As("b")
	if len(ids) > 0 {
		m = m.WhereIn("b.promotion_id", ids)
	}
	metricsDataJoinStr := "a.promotion_id = b.promotion_id "
	if startTime != "" {
		metricsDataJoinStr += "and a.create_date >= '" + startTime + "'"
	}
	if endTime != "" {
		metricsDataJoinStr += "and a.create_date <= '" + endTime + "'"
	}
	m = m.LeftJoin("ad_promotion_metrics_data", "a", metricsDataJoinStr)
	m = m.Group("b.promotion_id")
	filedStr := make([]string, 0)
	jsonStr := make([]string, 0)
	// 动态构建Field
	filedStr = append(filedStr, " any_value(b.advertiser_id) as advertiserId")
	filedStr = append(filedStr, " any_value(b.promotion_id) as promotionId")
	for _, metrics := range metricsName {
		if ok, jsonName := libUtils.CheckIsTableForField(metrics, entity.AdAdvertiserAccountMetricsData{}); ok {
			filedStr = append(filedStr, fmt.Sprintf("Sum(a.%s) as %s", metrics, jsonName))
			jsonStr = append(jsonStr, jsonName)
			// 字段较少暂时就写死
		} else if metrics == "balance" {
			filedStr = append(filedStr, fmt.Sprintf("any_value(b.%s) as %s", metrics, "balance"))
			jsonStr = append(jsonStr, "balance")
		} // todo 其他字段
	}
	var searchList []*model.AdPromotionReportDataRes
	err := m.Fields(filedStr).Scan(&searchList)
	if err != nil {
		g.Log().Errorf(ctx, "GetAdAdvertiserAccountReportStat2 -> %+v", err)
		return
	}
	if len(searchList) > 0 {
		list = make([]*model.MetricsByPlanTaskRes, 0)
		for _, item := range searchList {
			keyValue, keyValue2, innerError := libUtils.GetFieldsByTag(item, "json", jsonStr)
			if innerError != nil {
				continue
			}
			list = append(list, &model.MetricsByPlanTaskRes{
				AdId:         item.AdvertiserId,
				ObjectId:     item.PromotionId,
				KeyValuePare: keyValue,
				KeyNameMap:   keyValue2,
			})
		}
	}
	return
}

func (s *sAdPromotionMetricsData) List(ctx context.Context, req *model.AdPromotionMetricsDataSearchReq) (listRes *model.AdPromotionMetricsDataSearchRes, err error) {
	listRes = new(model.AdPromotionMetricsDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPromotionMetricsData.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.ShowCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ShowCnt+" = ?", gconv.Int64(req.ShowCnt))
		}
		if req.ClickCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ClickCnt+" = ?", gconv.Int64(req.ClickCnt))
		}
		if req.ConvertCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ConvertCnt+" = ?", gconv.Int64(req.ConvertCnt))
		}
		if req.DeepConvertCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().DeepConvertCnt+" = ?", gconv.Int64(req.DeepConvertCnt))
		}
		if req.AttributionConvertCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionConvertCnt+" = ?", gconv.Int64(req.AttributionConvertCnt))
		}
		if req.AttributionDeepConvertCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionDeepConvertCnt+" = ?", gconv.Int64(req.AttributionDeepConvertCnt))
		}
		if req.PreConvertCount != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().PreConvertCount+" = ?", gconv.Int64(req.PreConvertCount))
		}
		if req.ClickStartCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ClickStartCnt+" = ?", gconv.Int64(req.ClickStartCnt))
		}
		if req.DownloadFinishCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().DownloadFinishCnt+" = ?", gconv.Int64(req.DownloadFinishCnt))
		}
		if req.InstallFinishCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().InstallFinishCnt+" = ?", gconv.Int64(req.InstallFinishCnt))
		}
		if req.Active != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Active+" = ?", gconv.Int64(req.Active))
		}
		if req.GameAddiction != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().GameAddiction+" = ?", gconv.Int64(req.GameAddiction))
		}
		if req.AttributionNextDayOpenCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionNextDayOpenCnt+" = ?", gconv.Int64(req.AttributionNextDayOpenCnt))
		}
		if req.NextDayOpen != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().NextDayOpen+" = ?", gconv.Int64(req.NextDayOpen))
		}
		if req.ActivePay != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ActivePay+" = ?", gconv.Int64(req.ActivePay))
		}
		if req.GamePayCount != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().GamePayCount+" = ?", gconv.Int64(req.GamePayCount))
		}
		if req.AttributionGamePay7DCount != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionGamePay7DCount+" = ?", gconv.Int64(req.AttributionGamePay7DCount))
		}
		if req.AttributionActivePay7DPerCount != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionActivePay7DPerCount+" = ?", gconv.Int64(req.AttributionActivePay7DPerCount))
		}
		if req.InAppUv != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().InAppUv+" = ?", gconv.Int64(req.InAppUv))
		}
		if req.InAppDetailUv != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().InAppDetailUv+" = ?", gconv.Int64(req.InAppDetailUv))
		}
		if req.InAppCart != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().InAppCart+" = ?", gconv.Int64(req.InAppCart))
		}
		if req.InAppPay != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().InAppPay+" = ?", gconv.Int64(req.InAppPay))
		}
		if req.InAppOrder != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().InAppOrder+" = ?", gconv.Int64(req.InAppOrder))
		}
		if req.AttributionDayActivePayCount != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionDayActivePayCount+" = ?", gconv.Int64(req.AttributionDayActivePayCount))
		}
		if req.AttributionActivePayIntraOneDayCount != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionActivePayIntraOneDayCount+" = ?", gconv.Int64(req.AttributionActivePayIntraOneDayCount))
		}
		if req.AttributionRetention2DCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention2DCnt+" = ?", gconv.Int64(req.AttributionRetention2DCnt))
		}
		if req.AttributionRetention3DCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention3DCnt+" = ?", gconv.Int64(req.AttributionRetention3DCnt))
		}
		if req.AttributionRetention4DCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention4DCnt+" = ?", gconv.Int64(req.AttributionRetention4DCnt))
		}
		if req.AttributionRetention5DCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention5DCnt+" = ?", gconv.Int64(req.AttributionRetention5DCnt))
		}
		if req.AttributionRetention6DCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention6DCnt+" = ?", gconv.Int64(req.AttributionRetention6DCnt))
		}
		if req.AttributionRetention7DCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention7DCnt+" = ?", gconv.Int64(req.AttributionRetention7DCnt))
		}
		if req.AttributionRetention7DSumCnt != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().AttributionRetention7DSumCnt+" = ?", gconv.Int64(req.AttributionRetention7DSumCnt))
		}
		if req.TotalPlay != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().TotalPlay+" = ?", gconv.Int64(req.TotalPlay))
		}
		if req.ValidPlay != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().ValidPlay+" = ?", gconv.Int64(req.ValidPlay))
		}
		if req.Play25FeedBreak != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Play25FeedBreak+" = ?", gconv.Int64(req.Play25FeedBreak))
		}
		if req.Play50FeedBreak != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Play50FeedBreak+" = ?", gconv.Int64(req.Play50FeedBreak))
		}
		if req.Play75FeedBreak != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Play75FeedBreak+" = ?", gconv.Int64(req.Play75FeedBreak))
		}
		if req.Play99FeedBreak != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Play99FeedBreak+" = ?", gconv.Int64(req.Play99FeedBreak))
		}
		if req.CardShow != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().CardShow+" = ?", gconv.Int64(req.CardShow))
		}
		if req.DyLike != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().DyLike+" = ?", gconv.Int64(req.DyLike))
		}
		if req.DyComment != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().DyComment+" = ?", gconv.Int64(req.DyComment))
		}
		if req.DyShare != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().DyShare+" = ?", gconv.Int64(req.DyShare))
		}
		if req.IesChallengeClick != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().IesChallengeClick+" = ?", gconv.Int64(req.IesChallengeClick))
		}
		if req.IesMusicClick != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().IesMusicClick+" = ?", gconv.Int64(req.IesMusicClick))
		}
		if req.LocationClick != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().LocationClick+" = ?", gconv.Int64(req.LocationClick))
		}
		if req.CustomerEffective != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().CustomerEffective+" = ?", gconv.Int64(req.CustomerEffective))
		}
		if req.Wechat != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Wechat+" = ?", gconv.Int64(req.Wechat))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdPromotionMetricsData.Columns().CreatedAt+" >=? AND "+dao.AdPromotionMetricsData.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.CreateDate != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().CreateDate+" = ?", req.CreateDate)
		}
		if req.Register != "" {
			m = m.Where(dao.AdPromotionMetricsData.Columns().Register+" = ?", gconv.Int(req.Register))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPromotionMetricsDataListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPromotionMetricsDataListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdPromotionMetricsDataListRes{
				Id:                                    v.Id,
				AdvertiserId:                          v.AdvertiserId,
				PromotionId:                           v.PromotionId,
				ProjectId:                             v.ProjectId,
				StatCost:                              v.StatCost,
				ShowCnt:                               v.ShowCnt,
				CpmPlatform:                           v.CpmPlatform,
				ClickCnt:                              v.ClickCnt,
				Ctr:                                   v.Ctr,
				CpcPlatform:                           v.CpcPlatform,
				ConvertCnt:                            v.ConvertCnt,
				ConversionCost:                        v.ConversionCost,
				ConversionRate:                        v.ConversionRate,
				DeepConvertCnt:                        v.DeepConvertCnt,
				DeepConvertCost:                       v.DeepConvertCost,
				DeepConvertRate:                       v.DeepConvertRate,
				AttributionConvertCnt:                 v.AttributionConvertCnt,
				AttributionConvertCost:                v.AttributionConvertCost,
				AttributionDeepConvertCnt:             v.AttributionDeepConvertCnt,
				AttributionDeepConvertCost:            v.AttributionDeepConvertCost,
				PreConvertCount:                       v.PreConvertCount,
				PreConvertCost:                        v.PreConvertCost,
				PreConvertRate:                        v.PreConvertRate,
				ClickStartCnt:                         v.ClickStartCnt,
				ClickStartCost:                        v.ClickStartCost,
				ClickStartRate:                        v.ClickStartRate,
				DownloadFinishCnt:                     v.DownloadFinishCnt,
				DownloadFinishCost:                    v.DownloadFinishCost,
				DownloadFinishRate:                    v.DownloadFinishRate,
				InstallFinishCnt:                      v.InstallFinishCnt,
				InstallFinishCost:                     v.InstallFinishCost,
				InstallFinishRate:                     v.InstallFinishRate,
				Active:                                v.Active,
				ActiveCost:                            v.ActiveCost,
				ActiveRate:                            v.ActiveRate,
				ActiveRegisterCost:                    v.ActiveRegisterCost,
				ActiveRegisterRate:                    v.ActiveRegisterRate,
				GameAddiction:                         v.GameAddiction,
				GameAddictionCost:                     v.GameAddictionCost,
				GameAddictionRate:                     v.GameAddictionRate,
				AttributionNextDayOpenCnt:             v.AttributionNextDayOpenCnt,
				AttributionNextDayOpenCost:            v.AttributionNextDayOpenCost,
				AttributionNextDayOpenRate:            v.AttributionNextDayOpenRate,
				NextDayOpen:                           v.NextDayOpen,
				ActivePay:                             v.ActivePay,
				ActivePayCost:                         v.ActivePayCost,
				ActivePayRate:                         v.ActivePayRate,
				GamePayCount:                          v.GamePayCount,
				GamePayCost:                           v.GamePayCost,
				AttributionGamePay7DCount:             v.AttributionGamePay7DCount,
				AttributionGamePay7DCost:              v.AttributionGamePay7DCost,
				AttributionActivePay7DPerCount:        v.AttributionActivePay7DPerCount,
				InAppUv:                               v.InAppUv,
				InAppDetailUv:                         v.InAppDetailUv,
				InAppCart:                             v.InAppCart,
				InAppPay:                              v.InAppPay,
				InAppOrder:                            v.InAppOrder,
				AttributionGameInAppLtv1Day:           v.AttributionGameInAppLtv1Day,
				AttributionGameInAppLtv2Days:          v.AttributionGameInAppLtv2Days,
				AttributionGameInAppLtv3Days:          v.AttributionGameInAppLtv3Days,
				AttributionGameInAppLtv4Days:          v.AttributionGameInAppLtv4Days,
				AttributionGameInAppLtv5Days:          v.AttributionGameInAppLtv5Days,
				AttributionGameInAppLtv6Days:          v.AttributionGameInAppLtv6Days,
				AttributionGameInAppLtv7Days:          v.AttributionGameInAppLtv7Days,
				AttributionGameInAppLtv8Days:          v.AttributionGameInAppLtv8Days,
				AttributionGameInAppRoi1Day:           v.AttributionGameInAppRoi1Day,
				AttributionGameInAppRoi2Days:          v.AttributionGameInAppRoi2Days,
				AttributionGameInAppRoi3Days:          v.AttributionGameInAppRoi3Days,
				AttributionGameInAppRoi4Days:          v.AttributionGameInAppRoi4Days,
				AttributionGameInAppRoi5Days:          v.AttributionGameInAppRoi5Days,
				AttributionGameInAppRoi6Days:          v.AttributionGameInAppRoi6Days,
				AttributionGameInAppRoi7Days:          v.AttributionGameInAppRoi7Days,
				AttributionGameInAppRoi8Days:          v.AttributionGameInAppRoi8Days,
				AttributionDayActivePayCount:          v.AttributionDayActivePayCount,
				AttributionActivePayIntraOneDayCount:  v.AttributionActivePayIntraOneDayCount,
				AttributionActivePayIntraOneDayCost:   v.AttributionActivePayIntraOneDayCost,
				AttributionActivePayIntraOneDayRate:   v.AttributionActivePayIntraOneDayRate,
				AttributionActivePayIntraOneDayAmount: v.AttributionActivePayIntraOneDayAmount,
				AttributionActivePayIntraOneDayRoi:    v.AttributionActivePayIntraOneDayRoi,
				AttributionRetention2DCnt:             v.AttributionRetention2DCnt,
				AttributionRetention2DCost:            v.AttributionRetention2DCost,
				AttributionRetention2DRate:            v.AttributionRetention2DRate,
				AttributionRetention3DCnt:             v.AttributionRetention3DCnt,
				AttributionRetention3DCost:            v.AttributionRetention3DCost,
				AttributionRetention3DRate:            v.AttributionRetention3DRate,
				AttributionRetention4DCnt:             v.AttributionRetention4DCnt,
				AttributionRetention4DCost:            v.AttributionRetention4DCost,
				AttributionRetention4DRate:            v.AttributionRetention4DRate,
				AttributionRetention5DCnt:             v.AttributionRetention5DCnt,
				AttributionRetention5DCost:            v.AttributionRetention5DCost,
				AttributionRetention5DRate:            v.AttributionRetention5DRate,
				AttributionRetention6DCnt:             v.AttributionRetention6DCnt,
				AttributionRetention6DCost:            v.AttributionRetention6DCost,
				AttributionRetention6DRate:            v.AttributionRetention6DRate,
				AttributionRetention7DCnt:             v.AttributionRetention7DCnt,
				AttributionRetention7DCost:            v.AttributionRetention7DCost,
				AttributionRetention7DRate:            v.AttributionRetention7DRate,
				AttributionRetention7DSumCnt:          v.AttributionRetention7DSumCnt,
				AttributionRetention7DTotalCost:       v.AttributionRetention7DTotalCost,
				TotalPlay:                             v.TotalPlay,
				ValidPlay:                             v.ValidPlay,
				ValidPlayCost:                         v.ValidPlayCost,
				ValidPlayRate:                         v.ValidPlayRate,
				Play25FeedBreak:                       v.Play25FeedBreak,
				Play50FeedBreak:                       v.Play50FeedBreak,
				Play75FeedBreak:                       v.Play75FeedBreak,
				Play99FeedBreak:                       v.Play99FeedBreak,
				AveragePlayTimePerPlay:                v.AveragePlayTimePerPlay,
				PlayOverRate:                          v.PlayOverRate,
				WifiPlayRate:                          v.WifiPlayRate,
				CardShow:                              v.CardShow,
				DyLike:                                v.DyLike,
				DyComment:                             v.DyComment,
				DyShare:                               v.DyShare,
				IesChallengeClick:                     v.IesChallengeClick,
				IesMusicClick:                         v.IesMusicClick,
				LocationClick:                         v.LocationClick,
				CustomerEffective:                     v.CustomerEffective,
				Wechat:                                v.Wechat,
				AttributionMicroGame0DLtv:             v.AttributionMicroGame0DLtv,
				AttributionMicroGame3DLtv:             v.AttributionMicroGame3DLtv,
				AttributionMicroGame7DLtv:             v.AttributionMicroGame7DLtv,
				AttributionMicroGame0DRoi:             v.AttributionMicroGame0DRoi,
				AttributionMicroGame3DRoi:             v.AttributionMicroGame3DRoi,
				AttributionMicroGame7DRoi:             v.AttributionMicroGame7DRoi,
				CreatedAt:                             v.CreatedAt,
				CreateDate:                            v.CreateDate,
				TotalAmount:                           v.TotalAmount,
				TotalAdUp:                             v.TotalAdUp,
				Register:                              v.Register,
			}
		}
	})
	return
}

// AdPromotionReportReportStatTask 刷历史的报表数据
func (s *sAdPromotionMetricsData) AdPromotionReportReportStatTask(ctx context.Context, startTime string, endTime string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		for {
			if startTime > endTime {
				break
			}
			err1 := s.CreateAdAdvertiserPromotionReportStat(innerContext, startTime)
			if err1 != nil {
				g.Log().Error(ctx, err1)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}
func (s *sAdPromotionMetricsData) GetAdAdvertiserPromotionReportStat(ctx context.Context, pageNo int, pageSize int) (res []*model.AdAdvertiserAccountTokenRes, err error) {
	res = make([]*model.AdAdvertiserAccountTokenRes, 0)
	err = dao.AdPromotion.Ctx(ctx).As("a").
		LeftJoin("ad_majordomo_advertiser_account b", "a.majordomo_advertiser_account_id = b.id").
		Order("a.project_id").Fields(" a.advertiser_id ,a.project_id as projectId,a.promotion_id as promotionId , b.access_token").
		Page(pageNo, pageSize).Scan(&res)
	return
}

func (s *sAdPromotionMetricsData) UpdateMetricsByAdvertiserIds(ctx context.Context, advertiserIds []string, nowDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res := make([]*model.AdPromotionInfoRes, 0)
		err = dao.AdPromotion.Ctx(ctx).WhereIn(dao.AdProject.Columns().AdvertiserId, advertiserIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("查询广告计划失败：%v", err))
		// 根据advertiserId分组，分组后汇总promotionId
		groupMap := make(map[string][]string)
		for _, item := range res {
			// 判断map中是否含有item
			key := item.AdvertiserId
			if _, ok := groupMap[key]; ok {
				groupMap[key] = append(groupMap[key], item.PromotionId)
			} else {
				groupMap[key] = []string{item.PromotionId}
			}
		}
		for _, promotionIds := range groupMap {
			_ = s.UpdateMetricsByPIds(ctx, promotionIds, nowDate)
		}
	})
	return
}

// UpdateMetricsByPIds 编写根据promotion_ids 更新数据的方法
func (s *sAdPromotionMetricsData) UpdateMetricsByPIds(ctx context.Context, pIds []string, nowDate string) (err error) {
	res := make([]*model.AdAdvertiserAccountTokenRes, 0)
	err = dao.AdPromotion.Ctx(ctx).As("a").
		LeftJoin("ad_majordomo_advertiser_account b", "a.majordomo_advertiser_account_id = b.id").
		WhereIn("a.promotion_id", pIds).Fields(" a.advertiser_id ,a.project_id as projectId,a.promotion_id as promotionId , b.access_token").Scan(&res)
	updateMap := make(map[string][]string)
	list := make([]*model.AdPromotionMetricsDataInfoRes, 0)
	for _, item := range res {
		//判断map中是否含有 item.token
		key := item.AccessToken + "," + item.AdvertiserId + "," + item.ProjectId
		if _, ok := updateMap[key]; ok {
			updateMap[key] = append(updateMap[key], item.PromotionId)
		} else {
			updateMap[key] = []string{item.PromotionId}
		}
	}
	for key, ids := range updateMap {
		//nowDate := libUtils.GetNowDate()
		idAndTokenArray := strings.Split(key, ",")
		if len(idAndTokenArray) != 3 {
			g.Log().Errorf(ctx, "------------- idAndTokenArray  key:%v  --------------------", key)
			continue
		}
		accessToken, advertiserId, projectId := idAndTokenArray[0], idAndTokenArray[1], idAndTokenArray[2]
		for _, id := range ids {
			// 这个耗时较短在携程中进行处理
			var filters []*toutiaoModel.ReportCustomGetV30FiltersInner
			filters = append(filters, &toutiaoModel.ReportCustomGetV30FiltersInner{
				Field:    "cdp_promotion_id",
				Operator: 1,
				Type:     2,
				Values:   []string{id},
			})
			var orderBy []*toutiaoModel.ReportCustomGetV30OrderByInner
			orderByType, _ := toutiaoModel.NewOrderByTypeFromValue("Desc")
			orderBy = append(orderBy, &toutiaoModel.ReportCustomGetV30OrderByInner{
				Field: "stat_cost",
				Type:  orderByType,
			})

			if libUtils.IsNullOrEmpty(advertiserId) || libUtils.IsNullOrEmpty(accessToken) {
				g.Log().Errorf(ctx, "------------- advertiserId, accessToken   advertiserId:%v, accessToken:%v  --------------------", advertiserId, accessToken)
				continue
			}
			resp, e := advertiser.GetFiltersDayReportByMetrics(ctx, libUtils.StringToInt64(advertiserId), accessToken, nowDate, nowDate, filters, orderBy)
			if e != nil {
				g.Log().Errorf(ctx, "------------- GetToutiaoApiClient().GetFiltersDayReportByMetrics aid:%v , accessToken:%v, ERROR err：%v,resp:%v  --------------------", advertiserId, accessToken, e, resp)
				continue
			}
			if resp != nil && resp.TotalMetrics != nil && len(resp.Rows) > 0 {

				for _, row := range resp.Rows {
					thisEntity := new(model.AdPromotionMetricsDataInfoRes)
					err = libUtils.MapToStruct(row.Metrics, thisEntity)
					//jsonBytes, _ := jsoniter.Marshal(resp.TotalMetrics) // Map to JSON
					//err = jsoniter.Unmarshal(jsonBytes, &thisEntity)    // JSON to Struct
					if err != nil {
						g.Log().Errorf(ctx, "-------------  AdPromotionMetricsDataInfoRes MapToStruct ERROR err：%v,resp:%v  --------------------", err, resp)
						continue
					} else {
						thisEntity.AdvertiserId = advertiserId
						thisEntity.ProjectId = projectId
						thisEntity.PromotionId = id
						thisEntity.CreateDate = nowDate
						list = append(list, thisEntity)
					}
				}
			}
		}
	}
	if len(list) > 0 {
		_, err = dao.AdPromotionMetricsData.Ctx(ctx).Save(list)
	}
	return err

}

// CreateAdAdvertiserPromotionReportStat 刷历史数据 1.广告主账户报表数据
func (s *sAdPromotionMetricsData) CreateAdAdvertiserPromotionReportStat(ctx context.Context, statDate string) (err error) {
	statDate = strings.ReplaceAll(statDate, "'", "")
	channelRechargeStatKey := commonConsts.AdAdvertiserPromotionReport + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :ChannelStatTask ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNum = 1
		var pageSize = 500
		for {
			// 查询 ad_advertiser_account 表格查询出有token 的数据
			list, innerError := s.GetAdAdvertiserPromotionReportStat(ctx, pageNum, pageSize)
			if innerError != nil {
				return
			}
			if list == nil || len(list) == 0 {
				break
			}
			for _, item := range list {
				// 这个耗时较短在携程中进行处理
				var filters []*toutiaoModel.ReportCustomGetV30FiltersInner
				filters = append(filters, &toutiaoModel.ReportCustomGetV30FiltersInner{
					Field:    "cdp_promotion_id",
					Operator: 1,
					Type:     2,
					Values:   []string{item.PromotionId},
				})
				var orderBy []*toutiaoModel.ReportCustomGetV30OrderByInner
				orderByType, _ := toutiaoModel.NewOrderByTypeFromValue("Desc")
				orderBy = append(orderBy, &toutiaoModel.ReportCustomGetV30OrderByInner{
					Field: "stat_cost",
					Type:  orderByType,
				})
				resp, e := advertiser.GetFiltersDayReportByMetrics(ctx, libUtils.StringToInt64(item.AdvertiserId), item.AccessToken, statDate, statDate, filters, orderBy)
				if e != nil {
					g.Log().Errorf(ctx, "------------- GetToutiaoApiClient().GetFiltersDayReportByMetrics ERROR err：%v,resp:%v  --------------------", e, resp)
				}
				if resp != nil && resp.TotalMetrics != nil && len(resp.Rows) > 0 {
					thisEntity := new(model.AdPromotionMetricsDataInfoRes)
					err = libUtils.MapToStruct(resp.TotalMetrics, thisEntity)
					//jsonBytes, _ := jsoniter.Marshal(resp.TotalMetrics) // Map to JSON
					//err = jsoniter.Unmarshal(jsonBytes, &thisEntity)    // JSON to Struct
					if err != nil {
						g.Log().Errorf(ctx, "-------------  GetFiltersDayReportByMetrics MapToStruct ERROR err：%v,resp:%v  --------------------", err, resp)

					} else {
						thisEntity.AdvertiserId = item.AdvertiserId
						thisEntity.ProjectId = item.ProjectId
						thisEntity.PromotionId = item.PromotionId
						thisEntity.CreateDate = statDate
						_, err = dao.AdPromotionMetricsData.Ctx(ctx).Save(&thisEntity)
						return
					}

				}

			}
			pageNum++
			time.Sleep(time.Millisecond * 100)
		}

	})

	return err
}

func (s *sAdPromotionMetricsData) GetById(ctx context.Context, id int) (res *model.AdPromotionMetricsDataInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPromotionMetricsData.Ctx(ctx).WithAll().Where(dao.AdPromotionMetricsData.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPromotionMetricsData) Add(ctx context.Context, req *model.AdPromotionMetricsDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPromotionMetricsData.Ctx(ctx).Insert(do.AdPromotionMetricsData{
			AdvertiserId:                          req.AdvertiserId,
			PromotionId:                           req.PromotionId,
			ProjectId:                             req.ProjectId,
			StatCost:                              req.StatCost,
			ShowCnt:                               req.ShowCnt,
			CpmPlatform:                           req.CpmPlatform,
			ClickCnt:                              req.ClickCnt,
			Ctr:                                   req.Ctr,
			CpcPlatform:                           req.CpcPlatform,
			ConvertCnt:                            req.ConvertCnt,
			ConversionCost:                        req.ConversionCost,
			ConversionRate:                        req.ConversionRate,
			DeepConvertCnt:                        req.DeepConvertCnt,
			DeepConvertCost:                       req.DeepConvertCost,
			DeepConvertRate:                       req.DeepConvertRate,
			AttributionConvertCnt:                 req.AttributionConvertCnt,
			AttributionConvertCost:                req.AttributionConvertCost,
			AttributionDeepConvertCnt:             req.AttributionDeepConvertCnt,
			AttributionDeepConvertCost:            req.AttributionDeepConvertCost,
			PreConvertCount:                       req.PreConvertCount,
			PreConvertCost:                        req.PreConvertCost,
			PreConvertRate:                        req.PreConvertRate,
			ClickStartCnt:                         req.ClickStartCnt,
			ClickStartCost:                        req.ClickStartCost,
			ClickStartRate:                        req.ClickStartRate,
			DownloadFinishCnt:                     req.DownloadFinishCnt,
			DownloadFinishCost:                    req.DownloadFinishCost,
			DownloadFinishRate:                    req.DownloadFinishRate,
			InstallFinishCnt:                      req.InstallFinishCnt,
			InstallFinishCost:                     req.InstallFinishCost,
			InstallFinishRate:                     req.InstallFinishRate,
			Active:                                req.Active,
			ActiveCost:                            req.ActiveCost,
			ActiveRate:                            req.ActiveRate,
			ActiveRegisterCost:                    req.ActiveRegisterCost,
			ActiveRegisterRate:                    req.ActiveRegisterRate,
			GameAddiction:                         req.GameAddiction,
			GameAddictionCost:                     req.GameAddictionCost,
			GameAddictionRate:                     req.GameAddictionRate,
			AttributionNextDayOpenCnt:             req.AttributionNextDayOpenCnt,
			AttributionNextDayOpenCost:            req.AttributionNextDayOpenCost,
			AttributionNextDayOpenRate:            req.AttributionNextDayOpenRate,
			NextDayOpen:                           req.NextDayOpen,
			ActivePay:                             req.ActivePay,
			ActivePayCost:                         req.ActivePayCost,
			ActivePayRate:                         req.ActivePayRate,
			GamePayCount:                          req.GamePayCount,
			GamePayCost:                           req.GamePayCost,
			AttributionGamePay7DCount:             req.AttributionGamePay7DCount,
			AttributionGamePay7DCost:              req.AttributionGamePay7DCost,
			AttributionActivePay7DPerCount:        req.AttributionActivePay7DPerCount,
			InAppUv:                               req.InAppUv,
			InAppDetailUv:                         req.InAppDetailUv,
			InAppCart:                             req.InAppCart,
			InAppPay:                              req.InAppPay,
			InAppOrder:                            req.InAppOrder,
			AttributionGameInAppLtv1Day:           req.AttributionGameInAppLtv1Day,
			AttributionGameInAppLtv2Days:          req.AttributionGameInAppLtv2Days,
			AttributionGameInAppLtv3Days:          req.AttributionGameInAppLtv3Days,
			AttributionGameInAppLtv4Days:          req.AttributionGameInAppLtv4Days,
			AttributionGameInAppLtv5Days:          req.AttributionGameInAppLtv5Days,
			AttributionGameInAppLtv6Days:          req.AttributionGameInAppLtv6Days,
			AttributionGameInAppLtv7Days:          req.AttributionGameInAppLtv7Days,
			AttributionGameInAppLtv8Days:          req.AttributionGameInAppLtv8Days,
			AttributionGameInAppRoi1Day:           req.AttributionGameInAppRoi1Day,
			AttributionGameInAppRoi2Days:          req.AttributionGameInAppRoi2Days,
			AttributionGameInAppRoi3Days:          req.AttributionGameInAppRoi3Days,
			AttributionGameInAppRoi4Days:          req.AttributionGameInAppRoi4Days,
			AttributionGameInAppRoi5Days:          req.AttributionGameInAppRoi5Days,
			AttributionGameInAppRoi6Days:          req.AttributionGameInAppRoi6Days,
			AttributionGameInAppRoi7Days:          req.AttributionGameInAppRoi7Days,
			AttributionGameInAppRoi8Days:          req.AttributionGameInAppRoi8Days,
			AttributionDayActivePayCount:          req.AttributionDayActivePayCount,
			AttributionActivePayIntraOneDayCount:  req.AttributionActivePayIntraOneDayCount,
			AttributionActivePayIntraOneDayCost:   req.AttributionActivePayIntraOneDayCost,
			AttributionActivePayIntraOneDayRate:   req.AttributionActivePayIntraOneDayRate,
			AttributionActivePayIntraOneDayAmount: req.AttributionActivePayIntraOneDayAmount,
			AttributionActivePayIntraOneDayRoi:    req.AttributionActivePayIntraOneDayRoi,
			AttributionRetention2DCnt:             req.AttributionRetention2DCnt,
			AttributionRetention2DCost:            req.AttributionRetention2DCost,
			AttributionRetention2DRate:            req.AttributionRetention2DRate,
			AttributionRetention3DCnt:             req.AttributionRetention3DCnt,
			AttributionRetention3DCost:            req.AttributionRetention3DCost,
			AttributionRetention3DRate:            req.AttributionRetention3DRate,
			AttributionRetention4DCnt:             req.AttributionRetention4DCnt,
			AttributionRetention4DCost:            req.AttributionRetention4DCost,
			AttributionRetention4DRate:            req.AttributionRetention4DRate,
			AttributionRetention5DCnt:             req.AttributionRetention5DCnt,
			AttributionRetention5DCost:            req.AttributionRetention5DCost,
			AttributionRetention5DRate:            req.AttributionRetention5DRate,
			AttributionRetention6DCnt:             req.AttributionRetention6DCnt,
			AttributionRetention6DCost:            req.AttributionRetention6DCost,
			AttributionRetention6DRate:            req.AttributionRetention6DRate,
			AttributionRetention7DCnt:             req.AttributionRetention7DCnt,
			AttributionRetention7DCost:            req.AttributionRetention7DCost,
			AttributionRetention7DRate:            req.AttributionRetention7DRate,
			AttributionRetention7DSumCnt:          req.AttributionRetention7DSumCnt,
			AttributionRetention7DTotalCost:       req.AttributionRetention7DTotalCost,
			TotalPlay:                             req.TotalPlay,
			ValidPlay:                             req.ValidPlay,
			ValidPlayCost:                         req.ValidPlayCost,
			ValidPlayRate:                         req.ValidPlayRate,
			Play25FeedBreak:                       req.Play25FeedBreak,
			Play50FeedBreak:                       req.Play50FeedBreak,
			Play75FeedBreak:                       req.Play75FeedBreak,
			Play99FeedBreak:                       req.Play99FeedBreak,
			AveragePlayTimePerPlay:                req.AveragePlayTimePerPlay,
			PlayOverRate:                          req.PlayOverRate,
			WifiPlayRate:                          req.WifiPlayRate,
			CardShow:                              req.CardShow,
			DyLike:                                req.DyLike,
			DyComment:                             req.DyComment,
			DyShare:                               req.DyShare,
			IesChallengeClick:                     req.IesChallengeClick,
			IesMusicClick:                         req.IesMusicClick,
			LocationClick:                         req.LocationClick,
			CustomerEffective:                     req.CustomerEffective,
			Wechat:                                req.Wechat,
			AttributionMicroGame0DLtv:             req.AttributionMicroGame0DLtv,
			AttributionMicroGame3DLtv:             req.AttributionMicroGame3DLtv,
			AttributionMicroGame7DLtv:             req.AttributionMicroGame7DLtv,
			AttributionMicroGame0DRoi:             req.AttributionMicroGame0DRoi,
			AttributionMicroGame3DRoi:             req.AttributionMicroGame3DRoi,
			AttributionMicroGame7DRoi:             req.AttributionMicroGame7DRoi,
			CreateDate:                            req.CreateDate,
			TotalAmount:                           req.TotalAmount,
			TotalAdUp:                             req.TotalAdUp,
			Register:                              req.Register,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPromotionMetricsData) Edit(ctx context.Context, req *model.AdPromotionMetricsDataEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPromotionMetricsData.Ctx(ctx).WherePri(req.Id).Update(do.AdPromotionMetricsData{
			AdvertiserId:                          req.AdvertiserId,
			PromotionId:                           req.PromotionId,
			ProjectId:                             req.ProjectId,
			StatCost:                              req.StatCost,
			ShowCnt:                               req.ShowCnt,
			CpmPlatform:                           req.CpmPlatform,
			ClickCnt:                              req.ClickCnt,
			Ctr:                                   req.Ctr,
			CpcPlatform:                           req.CpcPlatform,
			ConvertCnt:                            req.ConvertCnt,
			ConversionCost:                        req.ConversionCost,
			ConversionRate:                        req.ConversionRate,
			DeepConvertCnt:                        req.DeepConvertCnt,
			DeepConvertCost:                       req.DeepConvertCost,
			DeepConvertRate:                       req.DeepConvertRate,
			AttributionConvertCnt:                 req.AttributionConvertCnt,
			AttributionConvertCost:                req.AttributionConvertCost,
			AttributionDeepConvertCnt:             req.AttributionDeepConvertCnt,
			AttributionDeepConvertCost:            req.AttributionDeepConvertCost,
			PreConvertCount:                       req.PreConvertCount,
			PreConvertCost:                        req.PreConvertCost,
			PreConvertRate:                        req.PreConvertRate,
			ClickStartCnt:                         req.ClickStartCnt,
			ClickStartCost:                        req.ClickStartCost,
			ClickStartRate:                        req.ClickStartRate,
			DownloadFinishCnt:                     req.DownloadFinishCnt,
			DownloadFinishCost:                    req.DownloadFinishCost,
			DownloadFinishRate:                    req.DownloadFinishRate,
			InstallFinishCnt:                      req.InstallFinishCnt,
			InstallFinishCost:                     req.InstallFinishCost,
			InstallFinishRate:                     req.InstallFinishRate,
			Active:                                req.Active,
			ActiveCost:                            req.ActiveCost,
			ActiveRate:                            req.ActiveRate,
			ActiveRegisterCost:                    req.ActiveRegisterCost,
			ActiveRegisterRate:                    req.ActiveRegisterRate,
			GameAddiction:                         req.GameAddiction,
			GameAddictionCost:                     req.GameAddictionCost,
			GameAddictionRate:                     req.GameAddictionRate,
			AttributionNextDayOpenCnt:             req.AttributionNextDayOpenCnt,
			AttributionNextDayOpenCost:            req.AttributionNextDayOpenCost,
			AttributionNextDayOpenRate:            req.AttributionNextDayOpenRate,
			NextDayOpen:                           req.NextDayOpen,
			ActivePay:                             req.ActivePay,
			ActivePayCost:                         req.ActivePayCost,
			ActivePayRate:                         req.ActivePayRate,
			GamePayCount:                          req.GamePayCount,
			GamePayCost:                           req.GamePayCost,
			AttributionGamePay7DCount:             req.AttributionGamePay7DCount,
			AttributionGamePay7DCost:              req.AttributionGamePay7DCost,
			AttributionActivePay7DPerCount:        req.AttributionActivePay7DPerCount,
			InAppUv:                               req.InAppUv,
			InAppDetailUv:                         req.InAppDetailUv,
			InAppCart:                             req.InAppCart,
			InAppPay:                              req.InAppPay,
			InAppOrder:                            req.InAppOrder,
			AttributionGameInAppLtv1Day:           req.AttributionGameInAppLtv1Day,
			AttributionGameInAppLtv2Days:          req.AttributionGameInAppLtv2Days,
			AttributionGameInAppLtv3Days:          req.AttributionGameInAppLtv3Days,
			AttributionGameInAppLtv4Days:          req.AttributionGameInAppLtv4Days,
			AttributionGameInAppLtv5Days:          req.AttributionGameInAppLtv5Days,
			AttributionGameInAppLtv6Days:          req.AttributionGameInAppLtv6Days,
			AttributionGameInAppLtv7Days:          req.AttributionGameInAppLtv7Days,
			AttributionGameInAppLtv8Days:          req.AttributionGameInAppLtv8Days,
			AttributionGameInAppRoi1Day:           req.AttributionGameInAppRoi1Day,
			AttributionGameInAppRoi2Days:          req.AttributionGameInAppRoi2Days,
			AttributionGameInAppRoi3Days:          req.AttributionGameInAppRoi3Days,
			AttributionGameInAppRoi4Days:          req.AttributionGameInAppRoi4Days,
			AttributionGameInAppRoi5Days:          req.AttributionGameInAppRoi5Days,
			AttributionGameInAppRoi6Days:          req.AttributionGameInAppRoi6Days,
			AttributionGameInAppRoi7Days:          req.AttributionGameInAppRoi7Days,
			AttributionGameInAppRoi8Days:          req.AttributionGameInAppRoi8Days,
			AttributionDayActivePayCount:          req.AttributionDayActivePayCount,
			AttributionActivePayIntraOneDayCount:  req.AttributionActivePayIntraOneDayCount,
			AttributionActivePayIntraOneDayCost:   req.AttributionActivePayIntraOneDayCost,
			AttributionActivePayIntraOneDayRate:   req.AttributionActivePayIntraOneDayRate,
			AttributionActivePayIntraOneDayAmount: req.AttributionActivePayIntraOneDayAmount,
			AttributionActivePayIntraOneDayRoi:    req.AttributionActivePayIntraOneDayRoi,
			AttributionRetention2DCnt:             req.AttributionRetention2DCnt,
			AttributionRetention2DCost:            req.AttributionRetention2DCost,
			AttributionRetention2DRate:            req.AttributionRetention2DRate,
			AttributionRetention3DCnt:             req.AttributionRetention3DCnt,
			AttributionRetention3DCost:            req.AttributionRetention3DCost,
			AttributionRetention3DRate:            req.AttributionRetention3DRate,
			AttributionRetention4DCnt:             req.AttributionRetention4DCnt,
			AttributionRetention4DCost:            req.AttributionRetention4DCost,
			AttributionRetention4DRate:            req.AttributionRetention4DRate,
			AttributionRetention5DCnt:             req.AttributionRetention5DCnt,
			AttributionRetention5DCost:            req.AttributionRetention5DCost,
			AttributionRetention5DRate:            req.AttributionRetention5DRate,
			AttributionRetention6DCnt:             req.AttributionRetention6DCnt,
			AttributionRetention6DCost:            req.AttributionRetention6DCost,
			AttributionRetention6DRate:            req.AttributionRetention6DRate,
			AttributionRetention7DCnt:             req.AttributionRetention7DCnt,
			AttributionRetention7DCost:            req.AttributionRetention7DCost,
			AttributionRetention7DRate:            req.AttributionRetention7DRate,
			AttributionRetention7DSumCnt:          req.AttributionRetention7DSumCnt,
			AttributionRetention7DTotalCost:       req.AttributionRetention7DTotalCost,
			TotalPlay:                             req.TotalPlay,
			ValidPlay:                             req.ValidPlay,
			ValidPlayCost:                         req.ValidPlayCost,
			ValidPlayRate:                         req.ValidPlayRate,
			Play25FeedBreak:                       req.Play25FeedBreak,
			Play50FeedBreak:                       req.Play50FeedBreak,
			Play75FeedBreak:                       req.Play75FeedBreak,
			Play99FeedBreak:                       req.Play99FeedBreak,
			AveragePlayTimePerPlay:                req.AveragePlayTimePerPlay,
			PlayOverRate:                          req.PlayOverRate,
			WifiPlayRate:                          req.WifiPlayRate,
			CardShow:                              req.CardShow,
			DyLike:                                req.DyLike,
			DyComment:                             req.DyComment,
			DyShare:                               req.DyShare,
			IesChallengeClick:                     req.IesChallengeClick,
			IesMusicClick:                         req.IesMusicClick,
			LocationClick:                         req.LocationClick,
			CustomerEffective:                     req.CustomerEffective,
			Wechat:                                req.Wechat,
			AttributionMicroGame0DLtv:             req.AttributionMicroGame0DLtv,
			AttributionMicroGame3DLtv:             req.AttributionMicroGame3DLtv,
			AttributionMicroGame7DLtv:             req.AttributionMicroGame7DLtv,
			AttributionMicroGame0DRoi:             req.AttributionMicroGame0DRoi,
			AttributionMicroGame3DRoi:             req.AttributionMicroGame3DRoi,
			AttributionMicroGame7DRoi:             req.AttributionMicroGame7DRoi,
			CreateDate:                            req.CreateDate,
			TotalAmount:                           req.TotalAmount,
			TotalAdUp:                             req.TotalAdUp,
			Register:                              req.Register,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdPromotionMetricsData) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPromotionMetricsData.Ctx(ctx).Delete(dao.AdPromotionMetricsData.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// OptimizerDataStatistics 优化师数据统计
func (s *sAdPromotionMetricsData) OptimizerDataStatistics(ctx context.Context, req *model.OptimizerDataStatisticsReq) (listRes *model.OptimizerDataStatisticsRes, err error) {
	listRes = new(model.OptimizerDataStatisticsRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPromotionMetricsData.Ctx(ctx).As("a").
			LeftJoin("ad_promotion", "b", "a.promotion_id = b.promotion_id").
			LeftJoin("sys_user", "u", "b.user_id = u.id").
			LeftJoin("sys_dept", "d", "d.dept_id = u.dept_id")
		if len(req.UserIds) > 0 {
			m = m.WhereIn("b.user_id", req.UserIds)
		} else {
			admin, userIds := GetUserIdsBySupervisorIds(ctx, req.SupervisorIds)
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("b.user_id", userIds)
			}
		}
		if req.StartTime != "" {
			m = m.WhereGTE("a.create_date", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.WhereLTE("a.create_date", req.EndTime)
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.AdvertiserIds) > 0 {
			m = m.WhereIn("a.advertiser_id", req.AdvertiserIds)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var groupBy string
		var orderBy string
		fields := make([]string, 0)
		groupBy = "b.user_id, a.create_date"
		orderBy = "statPayAmount desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields = append(fields, "ANY_VALUE(a.create_date) as createDate")
		fields = append(fields, "ANY_VALUE(b.user_id) as userId")
		fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		fields = append(fields, "ANY_VALUE(d.leader) as supervisorName")
		fields = append(fields, "COUNT(DISTINCT b.promotion_id) as totalAdNums")
		fields = append(fields, "COUNT(DISTINCT CASE WHEN a.stat_cost > 0 THEN a.promotion_id END) as hasCostAdNums")
		fields = append(fields, "COUNT(DISTINCT CASE WHEN b.learning_phase = 'LEARNED' THEN b.promotion_id END) as learnedAdNums")
		fields = append(fields, "ROUND(SUM(a.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost)*100,2) as payAmountRoi")
		fields = append(fields, "SUM(a.show_cnt) as showCnt")
		fields = append(fields, "ROUND(SUM(a.cpm_platform),2) as cpmPlatform")
		fields = append(fields, "SUM(a.click_cnt) as clickCnt")
		fields = append(fields, "ROUND(SUM(a.click_cnt)/SUM(a.show_cnt)*100,2) as ctr")
		fields = append(fields, "SUM(a.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(a.conversion_cost),2) as conversionCost")
		fields = append(fields, "ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "SUM(a.active) as active")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.active),2) as activeCost")
		fields = append(fields, "ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost)*100,2) as attributionGameInAppRoi1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost)*100,2) as attributionMicroGame0DRoi")
		fields = append(fields, "SUM(a.active_pay) as activePay")
		fields = append(fields, "ROUND(SUM(a.active_pay)/SUM(a.active)*100,2) as activePayRate")
		fields = append(fields, "SUM(a.game_pay_count) as gamePayCount")
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
	})
	return
}

func GetUserIdsBySupervisorIds(ctx context.Context, supervisorIds []uint64) (admin bool, userIds []int) {
	if len(supervisorIds) > 0 {
		users, _ := sysService.SysUser().GetUserByIds(ctx, supervisorIds)
		for _, v := range users {
			ids, _, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     v.Id,
					DeptId: v.DeptId,
				},
			})
			for _, k := range ids {
				userIds = append(userIds, k)
			}
		}
	} else {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ = sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
	}
	return
}
