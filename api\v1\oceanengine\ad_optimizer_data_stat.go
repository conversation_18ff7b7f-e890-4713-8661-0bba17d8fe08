// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: api/v1/oceanengine/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdOptimizerDataStatSearchReq 分页请求参数
type AdOptimizerDataStatSearchReq struct {
	g.Meta `path:"/list" tags:"优化师数据统计表" method:"post" summary:"优化师数据统计列表"`
	commonApi.Author
	model.AdOptimizerDataStatSearchReq
}

// AdOptimizerDataStatSearchRes 列表返回结果
type AdOptimizerDataStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdOptimizerDataStatSearchRes
}

// AdOptimizerSupervisorDataStatSearchReq 分页请求参数
type AdOptimizerSupervisorDataStatSearchReq struct {
	g.Meta `path:"/supervisor/list" tags:"优化师数据统计表" method:"post" summary:"优化师主管数据统计列表"`
	commonApi.Author
	model.AdOptimizerDataStatSearchReq
}

// AdOptimizerSupervisorDataStatSearchRes 列表返回结果
type AdOptimizerSupervisorDataStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdOptimizerDataStatSearchRes
}

// AdOptimizerDataStatAddReq 添加操作请求参数
type AdOptimizerDataStatAddReq struct {
	g.Meta `path:"/add" tags:"优化师数据统计表" method:"post" summary:"优化师数据统计表添加"`
	commonApi.Author
	*model.AdOptimizerDataStatAddReq
}

// AdOptimizerDataStatAddRes 添加操作返回结果
type AdOptimizerDataStatAddRes struct {
	commonApi.EmptyRes
}

// AdOptimizerDataStatTaskReq 优化师数据统计任务请求参数
type AdOptimizerDataStatTaskReq struct {
	g.Meta `path:"/task" tags:"优化师数据统计表" method:"post" summary:"优化师数据统计任务"`
	commonApi.Author
	model.AdOptimizerDataStatSearchReq
}

// AdOptimizerDataStatTaskRes 优化师数据统计任务返回结果
type AdOptimizerDataStatTaskRes struct {
	commonApi.EmptyRes
}
