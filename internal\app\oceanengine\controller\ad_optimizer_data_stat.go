// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/controller/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/oceanengine"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adOptimizerDataStatController struct {
	systemController.BaseController
}

var AdOptimizerDataStat = new(adOptimizerDataStatController)

// List 优化师数据统计列表
func (c *adOptimizerDataStatController) List(ctx context.Context, req *oceanengine.AdOptimizerDataStatSearchReq) (res *oceanengine.AdOptimizerDataStatSearchRes, err error) {
	res = new(oceanengine.AdOptimizerDataStatSearchRes)
	res.AdOptimizerDataStatSearchRes, err = service.AdOptimizerDataStat().List(ctx, &req.AdOptimizerDataStatSearchReq)
	return
}

// OptimizerSupervisorList 优化师主管数据统计列表
func (c *adOptimizerDataStatController) OptimizerSupervisorList(ctx context.Context, req *oceanengine.AdOptimizerSupervisorDataStatSearchReq) (res *oceanengine.AdOptimizerSupervisorDataStatSearchRes, err error) {
	res = new(oceanengine.AdOptimizerSupervisorDataStatSearchRes)
	res.AdOptimizerDataStatSearchRes, err = service.AdOptimizerDataStat().OptimizerSupervisorList(ctx, &req.AdOptimizerDataStatSearchReq)
	return
}

// Add 添加优化师数据统计表
func (c *adOptimizerDataStatController) Add(ctx context.Context, req *oceanengine.AdOptimizerDataStatAddReq) (res *oceanengine.AdOptimizerDataStatAddRes, err error) {
	err = service.AdOptimizerDataStat().Add(ctx, req.AdOptimizerDataStatAddReq)
	return
}

// RunAdOptimizerDataStat 优化师数据统计
func (c *adOptimizerDataStatController) RunAdOptimizerDataStat(ctx context.Context, req *oceanengine.AdOptimizerDataStatTaskReq) (res *oceanengine.AdOptimizerDataStatTaskRes, err error) {
	err = service.AdOptimizerDataStat().RunAdOptimizerDataStat(ctx, &req.AdOptimizerDataStatSearchReq)
	return
}
