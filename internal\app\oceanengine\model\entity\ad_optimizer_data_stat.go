// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/model/entity/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdOptimizerDataStat is the golang structure for table ad_optimizer_data_stat.
type AdOptimizerDataStat struct {
	gmeta.Meta                  `orm:"table:ad_optimizer_data_stat"`
	CreateDate                  string  `orm:"create_date" json:"createDate"`                                       // 统计日期
	AdvertiserId                int64   `orm:"advertiser_id" json:"advertiserId"`                                   // 广告账户ID
	UserId                      int     `orm:"user_id" json:"userId"`                                               // 优化师ID
	TotalAdNums                 int     `orm:"total_ad_nums" json:"totalAdNums"`                                    // 搭建广告数
	HasCostAdNums               int     `orm:"has_cost_ad_nums" json:"hasCostAdNums"`                               // 有消耗广告数
	LearnedAdNums               int     `orm:"learned_ad_nums" json:"learnedAdNums"`                                // 过学习期广告数
	StatCost                    float64 `orm:"stat_cost" json:"statCost"`                                           // 消耗
	StatPayAmount               float64 `orm:"stat_pay_amount" json:"statPayAmount"`                                // 付费金额（回传时间）
	ShowCnt                     int     `orm:"show_cnt" json:"showCnt"`                                             // 展示数
	ClickCnt                    int     `orm:"click_cnt" json:"clickCnt"`                                           // 点击数
	ConvertCnt                  int     `orm:"convert_cnt" json:"convertCnt"`                                       // 转化数
	Active                      int     `orm:"active" json:"active"`                                                // 激活数
	AttributionGameInAppLtv1Day float64 `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day"` // 付费金额（激活用户当日付费金额）
	AttributionMicroGame0DLtv   float64 `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv"`      // 小程序/小游戏当日LTV（激活用户当日LTV）
	ActivePay                   int     `orm:"active_pay" json:"activePay"`                                         // 首次付费数
	GamePayCount                int     `orm:"game_pay_count" json:"gamePayCount"`                                  // 付费次数
}
