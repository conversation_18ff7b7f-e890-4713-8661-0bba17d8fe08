// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/dao/internal/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdOptimizerDataStatDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdOptimizerDataStatDao struct {
	table   string                     // Table is the underlying table name of the DAO.
	group   string                     // Group is the database configuration group name of current DAO.
	columns AdOptimizerDataStatColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdOptimizerDataStatColumns defines and stores column names for table ad_optimizer_data_stat.
type AdOptimizerDataStatColumns struct {
	CreateDate                  string // 统计日期
	AdvertiserId                string // 广告账户ID
	UserId                      string // 优化师ID
	TotalAdNums                 string // 搭建广告数
	HasCostAdNums               string // 有消耗广告数
	LearnedAdNums               string // 过学习期广告数
	StatCost                    string // 消耗
	StatPayAmount               string // 付费金额（回传时间）
	ShowCnt                     string // 展示数
	ClickCnt                    string // 点击数
	ConvertCnt                  string // 转化数
	Active                      string // 激活数
	AttributionGameInAppLtv1Day string // 付费金额（激活用户当日付费金额）
	AttributionMicroGame0DLtv   string // 小程序/小游戏当日LTV（激活用户当日LTV）
	ActivePay                   string // 首次付费数
	GamePayCount                string // 付费次数
}

var adOptimizerDataStatColumns = AdOptimizerDataStatColumns{
	CreateDate:                  "create_date",
	AdvertiserId:                "advertiser_id",
	UserId:                      "user_id",
	TotalAdNums:                 "total_ad_nums",
	HasCostAdNums:               "has_cost_ad_nums",
	LearnedAdNums:               "learned_ad_nums",
	StatCost:                    "stat_cost",
	StatPayAmount:               "stat_pay_amount",
	ShowCnt:                     "show_cnt",
	ClickCnt:                    "click_cnt",
	ConvertCnt:                  "convert_cnt",
	Active:                      "active",
	AttributionGameInAppLtv1Day: "attribution_game_in_app_ltv_1day",
	AttributionMicroGame0DLtv:   "attribution_micro_game_0d_ltv",
	ActivePay:                   "active_pay",
	GamePayCount:                "game_pay_count",
}

// NewAdOptimizerDataStatDao creates and returns a new DAO object for table data access.
func NewAdOptimizerDataStatDao() *AdOptimizerDataStatDao {
	return &AdOptimizerDataStatDao{
		group:   "default",
		table:   "ad_optimizer_data_stat",
		columns: adOptimizerDataStatColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdOptimizerDataStatDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdOptimizerDataStatDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdOptimizerDataStatDao) Columns() AdOptimizerDataStatColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdOptimizerDataStatDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdOptimizerDataStatDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdOptimizerDataStatDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
