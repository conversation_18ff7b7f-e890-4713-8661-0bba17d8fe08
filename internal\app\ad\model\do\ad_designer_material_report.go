// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/model/entity/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdDesignerMaterialReport is the golang structure for table ad_designer_material_report.
type AdDesignerMaterialReport struct {
	gmeta.Meta           `orm:"table:ad_designer_material_report, do:true"`
	StatDate             interface{} `orm:"stat_date,primary" json:"statDate"`                  // 统计日期
	DesignerName         interface{} `orm:"designer_name,primary" json:"designerName"`          // 设计师名称
	DesignerId           interface{} `orm:"designer_id" json:"designerId"`                      // 设计师名称
	NewMaterialCount     interface{} `orm:"new_material_count" json:"newMaterialCount"`         // 上新素材数
	ExposedMaterialCount interface{} `orm:"exposed_material_count" json:"exposedMaterialCount"` // 曝光大于0的素材数
	NewMaterialCost      interface{} `orm:"new_material_cost" json:"newMaterialCost"`           // 上新素材消耗
	TotalCost            interface{} `orm:"total_cost" json:"totalCost"`                        // 总消耗
	Impressions          interface{} `orm:"impressions" json:"impressions"`                     // 展示数
	AvgCpm               interface{} `orm:"avg_cpm" json:"avgCpm"`                              // 平均千次展现成本
	Clicks               interface{} `orm:"clicks" json:"clicks"`                               // 点击数
	Ctr                  interface{} `orm:"ctr" json:"ctr"`                                     // 点击率
	Conversions          interface{} `orm:"conversions" json:"conversions"`                     // 转化数
	Cpa                  interface{} `orm:"cpa" json:"cpa"`                                     // 转化成本
	ConversionRate       interface{} `orm:"conversion_rate" json:"conversionRate"`              // 转化率
	Actives              interface{} `orm:"actives" json:"actives"`                             // 激活数
	ActiveCost           interface{} `orm:"active_cost" json:"activeCost"`                      // 激活成本
	ActiveRate           interface{} `orm:"active_rate" json:"activeRate"`                      // 激活率
	FirstPayCount        interface{} `orm:"first_pay_count" json:"firstPayCount"`               // 首次付费数
	FirstPayRate         interface{} `orm:"first_pay_rate" json:"firstPayRate"`                 // 首次付费率
}
