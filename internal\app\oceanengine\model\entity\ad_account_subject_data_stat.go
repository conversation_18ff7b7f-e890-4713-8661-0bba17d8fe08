// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/model/entity/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAccountSubjectDataStat is the golang structure for table ad_account_subject_data_stat.
type AdAccountSubjectDataStat struct {
	gmeta.Meta                  `orm:"table:ad_account_subject_data_stat"`
	CreateDate                  string  `orm:"create_date" json:"createDate"`                                       // 统计日期
	AdvertiserCompany           string  `orm:"advertiser_company" json:"advertiserCompany"`                         // 账户主体
	UserId                      int     `orm:"user_id" json:"userId"`                                               // 用户ID
	TotalAccounts               int     `orm:"total_accounts" json:"totalAccounts"`                                 // 总账户数
	ActiveAccounts              int     `orm:"active_accounts" json:"activeAccounts"`                               // 在投账户数
	StatCost                    float64 `orm:"stat_cost" json:"statCost"`                                           // 消耗
	StatPayAmount               float64 `orm:"stat_pay_amount" json:"statPayAmount"`                                // 付费金额（回传时间）
	Active                      int     `orm:"active" json:"active"`                                                // 激活数
	ClickCnt                    int     `orm:"click_cnt" json:"clickCnt"`                                           // 点击数
	ConvertCnt                  int     `orm:"convert_cnt" json:"convertCnt"`                                       // 转化数
	AttributionGameInAppLtv1Day float64 `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day"` // 付费金额
	AttributionMicroGame0DLtv   float64 `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv"`      // 小程序/小游戏当日LTV
}
