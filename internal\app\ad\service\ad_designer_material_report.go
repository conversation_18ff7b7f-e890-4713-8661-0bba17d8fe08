// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/service/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/ad"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IAdDesignerMaterialReport interface {
	List(ctx context.Context, req *model.AdDesignerMaterialReportSearchReq) (listRes *model.AdDesignerMaterialSearchRes, err error)
	AdDesignerMaterialReport(ctx context.Context, req *model.AdDesignerMaterialReportReq) (listRes *model.AdDesignerMaterialReportRes, err error)
	Add(ctx context.Context, req *model.AdDesignerMaterialReportAddReq) (err error)
	Edit(ctx context.Context, req *model.AdDesignerMaterialReportEditReq) (err error)
	PullData(ctx context.Context, req *ad.AdDesignerMaterialReportPullDataReq) (res *ad.AdDesignerMaterialReportPullDataRes, err error)
}

var localAdDesignerMaterialReport IAdDesignerMaterialReport

func AdDesignerMaterialReport() IAdDesignerMaterialReport {
	if localAdDesignerMaterialReport == nil {
		panic("implement not found for interface IAdDesignerMaterialReport, forgot register?")
	}
	return localAdDesignerMaterialReport
}

func RegisterAdDesignerMaterialReport(i IAdDesignerMaterialReport) {
	localAdDesignerMaterialReport = i
}
