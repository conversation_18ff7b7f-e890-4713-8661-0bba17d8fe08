// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-01-24 10:42:39
// 生成路径: internal/app/ad/service/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IAdHomepageData interface {
	CardData(ctx context.Context, req *model.AdHomepageCardDataReq) (res *model.AdHomepageCardDataRes, err error)
	OptimizerPieChartData(ctx context.Context, req *model.AdHomepageOptimizerPieChartDataReq) (res *model.AdHomepageOptimizerPieChartDataRes, err error)
	DesignerPieChartData(ctx context.Context, req *model.AdHomepageDesignerPieChartDataReq) (res *model.AdHomepageDesignerPieChartDataRes, err error)
	LineChartData(ctx context.Context, req *model.AdHomepageLineChartDataReq) (res *model.AdHomepageLineChartDataRes, err error)
}

var localAdHomepageData IAdHomepageData

func AdHomepageData() IAdHomepageData {
	if localAdHomepageData == nil {
		panic("implement not found for interface IAdHomepageData, forgot register?")
	}
	return localAdHomepageData
}

func RegisterAdHomepageData(i IAdHomepageData) {
	localAdHomepageData = i
}
