// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/model/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdAdvertiserAccountHourMetricsDataInfoRes is the golang structure for table ad_advertiser_account_hour_metrics_data.
type AdAdvertiserAccountHourMetricsDataInfoRes struct {
	gmeta.Meta    `orm:"table:ad_advertiser_account_hour_metrics_data"`
	AdvertiserId  string  `orm:"advertiser_id" json:"advertiserId" dc:"账户ID"`    // 账户ID
	CreateDate    string  `orm:"create_date" json:"createDate" dc:"创建日期"`        // 创建日期
	Hour          int     `orm:"hour" json:"hour" dc:"小时"`                       // 小时
	StatCost      float64 `orm:"stat_cost" json:"statCost" dc:"消耗"`              // 消耗
	StatPayAmount float64 `orm:"stat_pay_amount" json:"statPayAmount" dc:"付费金额"` // 付费金额
}

type AdAdvertiserAccountHourMetricsDataListRes struct {
	AdvertiserId  string  `json:"advertiserId" dc:"账户ID"`
	CreateDate    string  `json:"createDate" dc:"创建日期"`
	Hour          int     `json:"hour" dc:"小时"`
	StatCost      float64 `json:"statCost" dc:"消耗"`
	StatPayAmount float64 `json:"statPayAmount" dc:"付费金额"`
}

// AdAdvertiserAccountHourMetricsDataSearchReq 分页请求参数
type AdAdvertiserAccountHourMetricsDataSearchReq struct {
	comModel.PageReq
	AdvertiserId  string `p:"advertiserId" dc:"账户ID"`                                    //账户ID
	CreateDate    string `p:"createDate" dc:"创建日期"`                                      //创建日期
	Hour          string `p:"hour" v:"hour@integer#小时需为整数" dc:"小时"`                      //小时
	StatCost      string `p:"statCost" v:"statCost@float#消耗需为浮点数" dc:"消耗"`               //消耗
	StatPayAmount string `p:"statPayAmount" v:"statPayAmount@float#付费金额需为浮点数" dc:"付费金额"` //付费金额
}

// AdAdvertiserAccountHourMetricsDataSearchRes 列表返回结果
type AdAdvertiserAccountHourMetricsDataSearchRes struct {
	comModel.ListRes
	List []*AdAdvertiserAccountHourMetricsDataListRes `json:"list"`
}

// AdAdvertiserAccountHourMetricsDataAddReq 添加操作请求参数
type AdAdvertiserAccountHourMetricsDataAddReq struct {
	AdvertiserId  string  `p:"advertiserId" v:"required#账户ID不能为空" dc:"账户ID"`
	CreateDate    string  `p:"createDate" v:"required#创建日期不能为空" dc:"创建日期"`
	Hour          int     `p:"hour" v:"required#小时不能为空" dc:"小时"`
	StatCost      float64 `p:"statCost"  dc:"消耗"`
	StatPayAmount float64 `p:"statPayAmount"  dc:"付费金额"`
}

// AdAdvertiserAccountHourMetricsDataEditReq 修改操作请求参数
type AdAdvertiserAccountHourMetricsDataEditReq struct {
	AdvertiserId  string  `p:"advertiserId" v:"required#账户ID不能为空" dc:"账户ID"`
	CreateDate    string  `p:"createDate" v:"required#创建日期不能为空" dc:"创建日期"`
	Hour          int     `p:"hour" v:"required#小时不能为空" dc:"小时"`
	StatCost      float64 `p:"statCost"  dc:"消耗"`
	StatPayAmount float64 `p:"statPayAmount"  dc:"付费金额"`
}

type AdAdvertiserAccountHourMetricsDataTaskReq struct {
	StartTime    string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime      string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	Hour         *int   `p:"hour" dc:"小时 不传小时参数统计查询日期的所有小时数据"`
	AdvertiserId string `p:"advertiserId" dc:"广告账户ID"`
}
