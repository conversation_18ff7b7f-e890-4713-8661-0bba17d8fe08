// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-01-24 10:42:38
// 生成路径: internal/app/ad/model/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

type AdHomepageDataListRes struct {
	Id           int         `json:"id" dc:"ID"`
	Title        string      `json:"title" dc:"标题"`
	Content      string      `json:"content" dc:"内容"`
	ImageUrl     string      `json:"imageUrl" dc:"图片链接"`
	LinkUrl      string      `json:"linkUrl" dc:"跳转链接"`
	Platform     string      `json:"platform" dc:"广告平台"`
	Status       int         `json:"status" dc:"状态：1启用 0禁用"`
	Sort         int         `json:"sort" dc:"排序"`
	ViewCount    int         `json:"viewCount" dc:"浏览次数"`
	ClickCount   int         `json:"clickCount" dc:"点击次数"`
	ConvertCount int         `json:"convertCount" dc:"转化次数"`
	CreatedAt    *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdHomepageCardDataReq 分页请求参数
type AdHomepageCardDataReq struct {
}

// AdHomepageCardDataRes 列表返回结果
type AdHomepageCardDataRes struct {
	StatCostInfo    StatCostInfo    `json:"statCostInfo" dc:"卡片消耗数据"`
	ShowCntInfo     ShowCntInfo     `json:"showCntInfo" dc:"卡片展示数据"`
	ConvertCntInfo  ConvertCntInfo  `json:"convertCntInfo" dc:"卡片转化数据"`
	MaterialNumInfo MaterialNumInfo `json:"materialNumInfo" dc:"卡片素材数据"`
}

// StatCostInfo 卡片消耗数据
type StatCostInfo struct {
	TodayStatCost     float64 `json:"todayStatCost" dc:"今日消耗"`
	YesterdayStatCost float64 `json:"yesterdayStatCost" dc:"昨日消耗"`
	YearOnYear        float64 `json:"yearOnYear" dc:"同比"`
}

// ShowCntInfo 卡片展示数据
type ShowCntInfo struct {
	TodayShowCnt     int64   `json:"todayShowCnt" dc:"今日展示数"`
	YesterdayShowCnt int64   `json:"yesterdayShowCnt" dc:"昨日展示数"`
	YearOnYear       float64 `json:"yearOnYear" dc:"同比"`
}

// ConvertCntInfo 卡片转化数据
type ConvertCntInfo struct {
	TodayConvertCnt     int64   `json:"todayConvertCnt" dc:"今日转化数"`
	YesterdayConvertCnt int64   `json:"yesterdayConvertCnt" dc:"昨日转化数"`
	YearOnYear          float64 `json:"yearOnYear" dc:"同比"`
}

// MaterialNumInfo 卡片素材数据
type MaterialNumInfo struct {
	TodayMaterialNum int `json:"todayMaterialNum" dc:"今日上新素材数"`
	TotalMaterialNum int `json:"totalMaterialNum" dc:"累计素材数"`
}

// AdHomepageOptimizerPieChartDataReq 优化师饼状图数据请求参数
type AdHomepageOptimizerPieChartDataReq struct {
	StartTime  string `json:"startTime" dc:"开始时间"`
	EndTime    string `json:"endTime" dc:"结束时间"`
	MetricType int    `json:"metricType" dc:"指标类型 1: 消耗 2: 搭建广告数"`
}

type AdHomepageOptimizerPieChartDataRes struct {
	Data []PieChartData `json:"data" dc:"数据"`
}

// AdHomepageDesignerPieChartDataReq 设计师饼状图数据请求参数
type AdHomepageDesignerPieChartDataReq struct {
	StartTime  string `json:"startTime" dc:"开始时间"`
	EndTime    string `json:"endTime" dc:"结束时间"`
	MetricType int    `json:"metricType" dc:"指标类型 1: 上新素材数 2: 总消耗"`
}

type AdHomepageDesignerPieChartDataRes struct {
	Data []PieChartData `json:"data" dc:"数据"`
}

type PieChartData struct {
	Name       string  `json:"name" dc:"名称"`
	Value      float64 `json:"value" dc:"值"`
	Percentage string  `json:"percentage" dc:"百分比"`
}

type AdHomepageLineChartDataReq struct {
	StartTime string `json:"startTime" dc:"开始时间"`
	EndTime   string `json:"endTime" dc:"结束时间"`
	Dimension int    `json:"dimension" dc:"统计维度 1: 按年 2: 按月 3: 按周 4: 按天"`
}

type AdHomepageLineChartDataRes struct {
	Data []LineChartData `json:"data" dc:"数据"`
}

type LineChartData struct {
	Date          string  `json:"date" dc:"日期"`
	Month         int     `json:"month" dc:"月份"`
	Hour          int     `json:"hour" dc:"小时"`
	StatCost      float64 `json:"statCost" dc:"消耗"`
	StatPayAmount float64 `json:"statPayAmount" dc:"付费金额"`
	PayAmountRoi  float64 `json:"payAmountRoi" dc:"付费ROI"`
}
