// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: api/v1/ad/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdDesignerMaterialReportSearchReq 分页请求参数
type AdDesignerMaterialReportSearchReq struct {
	g.Meta `path:"/list" tags:"设计师素材数据统计" method:"post" summary:"设计师素材数据统计列表"`
	commonApi.Author
	model.AdDesignerMaterialReportSearchReq
}

// AdDesignerMaterialReportSearchRes 列表返回结果
type AdDesignerMaterialReportSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdDesignerMaterialSearchRes
}

// 设计师数据统计
type AdDesignerMaterialReportReq struct {
	g.Meta `path:"/report" tags:"设计师素材数据统计" method:"post" summary:"设计师主管素材数据统计信息"`
	commonApi.Author
	*model.AdDesignerMaterialReportReq
}
type AdDesignerMaterialReportRes struct {
	g.Meta `mime:"application/json"`
	*model.AdDesignerMaterialReportRes
}

// AdDesignerMaterialReportAddReq 添加操作请求参数
type AdDesignerMaterialReportAddReq struct {
	g.Meta `path:"/add" tags:"设计师素材数据统计" method:"post" summary:"设计师素材数据统计添加"`
	commonApi.Author
	*model.AdDesignerMaterialReportAddReq
}

// AdDesignerMaterialReportAddRes 添加操作返回结果
type AdDesignerMaterialReportAddRes struct {
	commonApi.EmptyRes
}

// AdDesignerMaterialReportEditReq 修改操作请求参数
type AdDesignerMaterialReportEditReq struct {
	g.Meta `path:"/edit" tags:"设计师素材数据统计" method:"put" summary:"设计师素材数据统计修改"`
	commonApi.Author
	*model.AdDesignerMaterialReportEditReq
}

// AdDesignerMaterialReportEditRes 修改操作返回结果
type AdDesignerMaterialReportEditRes struct {
	commonApi.EmptyRes
}

type AdDesignerMaterialReportPullDataReq struct {
	g.Meta `path:"/pullOrderData" tags:"设计师素材数据统计" method:"get" summary:"拉取数据"`
	commonApi.Author
	StartTime string `p:"startTime" v:"required#开始时间不能为空"`
	EndTime   string `p:"endTime" v:"required#结束时间不能为空"`
}

type AdDesignerMaterialReportPullDataRes struct {
	commonApi.EmptyRes
}
