// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-19 10:47:08
// 生成路径: internal/app/oceanengine/model/ad_promotion_metrics_data.go
// 生成人：cyao
// desc:广告账户下的广告的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdPromotionMetricsDataInfoRes is the golang structure for table ad_promotion_metrics_data.
type AdPromotionMetricsDataInfoRes struct {
	gmeta.Meta                            `orm:"table:ad_promotion_metrics_data"`
	Id                                    int         `orm:"id,primary" json:"id" dc:"自增id主键"`                                                                         // 自增id主键
	AdvertiserId                          string      `orm:"advertiser_id" json:"advertiserId" dc:"账户ID"`                                                              // 账户ID
	PromotionId                           string      `orm:"promotion_id" json:"promotionId" dc:"广告ID"`                                                                // 广告ID
	ProjectId                             string      `orm:"project_id" json:"projectId" dc:"项目ID"`                                                                    // 项目ID
	StatCost                              float64     `orm:"stat_cost" json:"statCost" dc:"消耗"`                                                                        // 消耗
	ShowCnt                               int64       `orm:"show_cnt" json:"showCnt" dc:"展示数"`                                                                         // 展示数
	CpmPlatform                           float64     `orm:"cpm_platform" json:"cpmPlatform" dc:"平均千次展现费用(元)"`                                                         // 平均千次展现费用(元)
	ClickCnt                              int64       `orm:"click_cnt" json:"clickCnt" dc:"点击数"`                                                                       // 点击数
	Ctr                                   float64     `orm:"ctr" json:"ctr" dc:"点击率"`                                                                                  // 点击率
	CpcPlatform                           float64     `orm:"cpc_platform" json:"cpcPlatform" dc:"平均点击单价(元)"`                                                           // 平均点击单价(元)
	ConvertCnt                            int64       `orm:"convert_cnt" json:"convertCnt" dc:"转化数"`                                                                   // 转化数
	ConversionCost                        float64     `orm:"conversion_cost" json:"conversionCost" dc:"平均转化成本"`                                                        // 平均转化成本
	ConversionRate                        float64     `orm:"conversion_rate" json:"conversionRate" dc:"转化率"`                                                           // 转化率
	DeepConvertCnt                        int64       `orm:"deep_convert_cnt" json:"deepConvertCnt" dc:"深度转化数"`                                                        // 深度转化数
	DeepConvertCost                       float64     `orm:"deep_convert_cost" json:"deepConvertCost" dc:"深度转化成本"`                                                     // 深度转化成本
	DeepConvertRate                       float64     `orm:"deep_convert_rate" json:"deepConvertRate" dc:"深度转化率"`                                                      // 深度转化率
	AttributionConvertCnt                 int64       `orm:"attribution_convert_cnt" json:"attributionConvertCnt" dc:"转化数(计费时间)"`                                      // 转化数(计费时间)
	AttributionConvertCost                float64     `orm:"attribution_convert_cost" json:"attributionConvertCost" dc:"转化成本(计费时间)"`                                   // 转化成本(计费时间)
	AttributionDeepConvertCnt             int64       `orm:"attribution_deep_convert_cnt" json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`                           // 深度转化数(计费时间)
	AttributionDeepConvertCost            float64     `orm:"attribution_deep_convert_cost" json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`                        // 深度转化成本(计费时间)
	PreConvertCount                       int64       `orm:"pre_convert_count" json:"preConvertCount" dc:"预估转化数(计费时间)"`                                                // 预估转化数(计费时间)
	PreConvertCost                        float64     `orm:"pre_convert_cost" json:"preConvertCost" dc:"预估转化成本(计费时间)"`                                                 // 预估转化成本(计费时间)
	PreConvertRate                        float64     `orm:"pre_convert_rate" json:"preConvertRate" dc:"预估转化率(计费时间)"`                                                  // 预估转化率(计费时间)
	ClickStartCnt                         int64       `orm:"click_start_cnt" json:"clickStartCnt" dc:"安卓下载开始数"`                                                        // 安卓下载开始数
	ClickStartCost                        float64     `orm:"click_start_cost" json:"clickStartCost" dc:"安卓下载开始成本"`                                                     // 安卓下载开始成本
	StatPayAmount                         float64     `orm:"stat_pay_amount" json:"statPayAmount,omitempty"`                                                           // 付费金额（回传时间）
	ClickStartRate                        float64     `orm:"click_start_rate" json:"clickStartRate" dc:"安卓下载开始率"`                                                      // 安卓下载开始率
	DownloadFinishCnt                     int64       `orm:"download_finish_cnt" json:"downloadFinishCnt" dc:"安卓下载完成数"`                                                // 安卓下载完成数
	DownloadFinishCost                    float64     `orm:"download_finish_cost" json:"downloadFinishCost" dc:"安卓下载完成成本"`                                             // 安卓下载完成成本
	DownloadFinishRate                    float64     `orm:"download_finish_rate" json:"downloadFinishRate" dc:"安卓下载完成率"`                                              // 安卓下载完成率
	InstallFinishCnt                      int64       `orm:"install_finish_cnt" json:"installFinishCnt" dc:"安卓安装完成数"`                                                  // 安卓安装完成数
	InstallFinishCost                     float64     `orm:"install_finish_cost" json:"installFinishCost" dc:"安卓安装完成成本"`                                               // 安卓安装完成成本
	InstallFinishRate                     float64     `orm:"install_finish_rate" json:"installFinishRate" dc:"安卓安装完成率"`                                                // 安卓安装完成率
	Active                                int64       `orm:"active" json:"active" dc:"激活数"`                                                                            // 激活数
	ActiveCost                            float64     `orm:"active_cost" json:"activeCost" dc:"激活成本"`                                                                  // 激活成本
	ActiveRate                            float64     `orm:"active_rate" json:"activeRate" dc:"激活率"`                                                                   // 激活率
	ActiveRegisterCost                    float64     `orm:"active_register_cost" json:"activeRegisterCost" dc:"注册成本"`                                                 // 注册成本
	ActiveRegisterRate                    float64     `orm:"active_register_rate" json:"activeRegisterRate" dc:"注册率"`                                                  // 注册率
	GameAddiction                         int64       `orm:"game_addiction" json:"gameAddiction" dc:"关键行为数"`                                                           // 关键行为数
	GameAddictionCost                     float64     `orm:"game_addiction_cost" json:"gameAddictionCost" dc:"关键行为成本"`                                                 // 关键行为成本
	GameAddictionRate                     float64     `orm:"game_addiction_rate" json:"gameAddictionRate" dc:"关键行为率"`                                                  // 关键行为率
	AttributionNextDayOpenCnt             int64       `orm:"attribution_next_day_open_cnt" json:"attributionNextDayOpenCnt" dc:"次留数"`                                  // 次留数
	AttributionNextDayOpenCost            float64     `orm:"attribution_next_day_open_cost" json:"attributionNextDayOpenCost" dc:"次留成本"`                               // 次留成本
	AttributionNextDayOpenRate            float64     `orm:"attribution_next_day_open_rate" json:"attributionNextDayOpenRate" dc:"次留率"`                                // 次留率
	NextDayOpen                           int64       `orm:"next_day_open" json:"nextDayOpen" dc:"次留回传数"`                                                              // 次留回传数
	ActivePay                             int64       `orm:"active_pay" json:"activePay" dc:"首次付费数"`                                                                   // 首次付费数
	ActivePayCost                         float64     `orm:"active_pay_cost" json:"activePayCost" dc:"首次付费成本"`                                                         // 首次付费成本
	ActivePayRate                         float64     `orm:"active_pay_rate" json:"activePayRate" dc:"首次付费率"`                                                          // 首次付费率
	GamePayCount                          int64       `orm:"game_pay_count" json:"gamePayCount" dc:"付费次数"`                                                             // 付费次数
	GamePayCost                           float64     `orm:"game_pay_cost" json:"gamePayCost" dc:"付费成本"`                                                               // 付费成本
	AttributionGamePay7DCount             int64       `orm:"attribution_game_pay_7d_count" json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`                         // 7日付费次数(激活时间)
	AttributionGamePay7DCost              float64     `orm:"attribution_game_pay_7d_cost" json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`                           // 7日付费成本(激活时间)
	AttributionActivePay7DPerCount        float64     `orm:"attribution_active_pay_7d_per_count" json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`            // 7日人均付费次数(激活时间)
	InAppUv                               int64       `orm:"in_app_uv" json:"inAppUv" dc:"APP内访问"`                                                                     // APP内访问
	InAppDetailUv                         int64       `orm:"in_app_detail_uv" json:"inAppDetailUv" dc:"APP内访问详情页"`                                                     // APP内访问详情页
	InAppCart                             int64       `orm:"in_app_cart" json:"inAppCart" dc:"APP内加入购物车"`                                                              // APP内加入购物车
	InAppPay                              int64       `orm:"in_app_pay" json:"inAppPay" dc:"APP内付费"`                                                                   // APP内付费
	InAppOrder                            int64       `orm:"in_app_order" json:"inAppOrder" dc:"APP内下单"`                                                               // APP内下单
	AttributionGameInAppLtv1Day           float64     `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`                          // 当日付费金额
	AttributionGameInAppLtv2Days          float64     `orm:"attribution_game_in_app_ltv_2days" json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`                     // 激活后一日付费金额
	AttributionGameInAppLtv3Days          float64     `orm:"attribution_game_in_app_ltv_3days" json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`                     // 激活后二日付费金额
	AttributionGameInAppLtv4Days          float64     `orm:"attribution_game_in_app_ltv_4days" json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`                     // 激活后三日付费金额
	AttributionGameInAppLtv5Days          float64     `orm:"attribution_game_in_app_ltv_5days" json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`                     // 激活后四日付费金额
	AttributionGameInAppLtv6Days          float64     `orm:"attribution_game_in_app_ltv_6days" json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`                     // 激活后五日付费金额
	AttributionGameInAppLtv7Days          float64     `orm:"attribution_game_in_app_ltv_7days" json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`                     // 激活后六日付费金额
	AttributionGameInAppLtv8Days          float64     `orm:"attribution_game_in_app_ltv_8days" json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`                     // 激活后七日付费金额
	AttributionGameInAppRoi1Day           float64     `orm:"attribution_game_in_app_roi_1day" json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`                         // 当日付费ROI
	AttributionGameInAppRoi2Days          float64     `orm:"attribution_game_in_app_roi_2days" json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`                    // 激活后一日付费ROI
	AttributionGameInAppRoi3Days          float64     `orm:"attribution_game_in_app_roi_3days" json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`                    // 激活后二日付费ROI
	AttributionGameInAppRoi4Days          float64     `orm:"attribution_game_in_app_roi_4days" json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`                    // 激活后三日付费ROI
	AttributionGameInAppRoi5Days          float64     `orm:"attribution_game_in_app_roi_5days" json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`                    // 激活后四日付费ROI
	AttributionGameInAppRoi6Days          float64     `orm:"attribution_game_in_app_roi_6days" json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`                    // 激活后五日付费ROI
	AttributionGameInAppRoi7Days          float64     `orm:"attribution_game_in_app_roi_7days" json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`                    // 激活后六日付费ROI
	AttributionGameInAppRoi8Days          float64     `orm:"attribution_game_in_app_roi_8days" json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`                    // 激活后七日付费ROI
	AttributionDayActivePayCount          int64       `orm:"attribution_day_active_pay_count" json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`                   // 计费当日激活且首次付费数
	AttributionActivePayIntraOneDayCount  int64       `orm:"attribution_active_pay_intra_one_day_count" json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`  // 激活后24h首次付费数
	AttributionActivePayIntraOneDayCost   float64     `orm:"attribution_active_pay_intra_one_day_cost" json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`   // 激活后24h首次付费成本
	AttributionActivePayIntraOneDayRate   float64     `orm:"attribution_active_pay_intra_one_day_rate" json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`    // 激活后24h首次付费率
	AttributionActivePayIntraOneDayAmount float64     `orm:"attribution_active_pay_intra_one_day_amount" json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"` // 激活后24h付费金额
	AttributionActivePayIntraOneDayRoi    float64     `orm:"attribution_active_pay_intra_one_day_roi" json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`      // 激活后24h付费ROI
	AttributionRetention2DCnt             int64       `orm:"attribution_retention_2d_cnt" json:"attributionRetention2DCnt" dc:"2日留存数"`                                 // 2日留存数
	AttributionRetention2DCost            float64     `orm:"attribution_retention_2d_cost" json:"attributionRetention2DCost" dc:"2日留存成本"`                              // 2日留存成本
	AttributionRetention2DRate            float64     `orm:"attribution_retention_2d_rate" json:"attributionRetention2DRate" dc:"2日留存率"`                               // 2日留存率
	AttributionRetention3DCnt             int64       `orm:"attribution_retention_3d_cnt" json:"attributionRetention3DCnt" dc:"3日留存数"`                                 // 3日留存数
	AttributionRetention3DCost            float64     `orm:"attribution_retention_3d_cost" json:"attributionRetention3DCost" dc:"3日留存成本"`                              // 3日留存成本
	AttributionRetention3DRate            float64     `orm:"attribution_retention_3d_rate" json:"attributionRetention3DRate" dc:"3日留存率"`                               // 3日留存率
	AttributionRetention4DCnt             int64       `orm:"attribution_retention_4d_cnt" json:"attributionRetention4DCnt" dc:"4日留存数"`                                 // 4日留存数
	AttributionRetention4DCost            float64     `orm:"attribution_retention_4d_cost" json:"attributionRetention4DCost" dc:"4日留存成本"`                              // 4日留存成本
	AttributionRetention4DRate            float64     `orm:"attribution_retention_4d_rate" json:"attributionRetention4DRate" dc:"4日留存率"`                               // 4日留存率
	AttributionRetention5DCnt             int64       `orm:"attribution_retention_5d_cnt" json:"attributionRetention5DCnt" dc:"5日留存数"`                                 // 5日留存数
	AttributionRetention5DCost            float64     `orm:"attribution_retention_5d_cost" json:"attributionRetention5DCost" dc:"5日留存成本"`                              // 5日留存成本
	AttributionRetention5DRate            float64     `orm:"attribution_retention_5d_rate" json:"attributionRetention5DRate" dc:"5日留存率"`                               // 5日留存率
	AttributionRetention6DCnt             int64       `orm:"attribution_retention_6d_cnt" json:"attributionRetention6DCnt" dc:"6日留存数"`                                 // 6日留存数
	AttributionRetention6DCost            float64     `orm:"attribution_retention_6d_cost" json:"attributionRetention6DCost" dc:"6日留存成本"`                              // 6日留存成本
	AttributionRetention6DRate            float64     `orm:"attribution_retention_6d_rate" json:"attributionRetention6DRate" dc:"6日留存率"`                               // 6日留存率
	AttributionRetention7DCnt             int64       `orm:"attribution_retention_7d_cnt" json:"attributionRetention7DCnt" dc:"7日留存数"`                                 // 7日留存数
	AttributionRetention7DCost            float64     `orm:"attribution_retention_7d_cost" json:"attributionRetention7DCost" dc:"7日留存成本"`                              // 7日留存成本
	AttributionRetention7DRate            float64     `orm:"attribution_retention_7d_rate" json:"attributionRetention7DRate" dc:"7日留存率"`                               // 7日留存率
	AttributionRetention7DSumCnt          int64       `orm:"attribution_retention_7d_sum_cnt" json:"attributionRetention7DSumCnt" dc:"7日留存总数"`                         // 7日留存总数
	AttributionRetention7DTotalCost       float64     `orm:"attribution_retention_7d_total_cost" json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`                  // 7日留存总成本
	TotalPlay                             int64       `orm:"total_play" json:"totalPlay" dc:"播放量"`                                                                     // 播放量
	ValidPlay                             int64       `orm:"valid_play" json:"validPlay" dc:"有效播放数"`                                                                   // 有效播放数
	ValidPlayCost                         float64     `orm:"valid_play_cost" json:"validPlayCost" dc:"有效播放成本"`                                                         // 有效播放成本
	ValidPlayRate                         float64     `orm:"valid_play_rate" json:"validPlayRate" dc:"有效播放率"`                                                          // 有效播放率
	Play25FeedBreak                       int64       `orm:"play_25_feed_break" json:"play25FeedBreak" dc:"25%进度播放数"`                                                  // 25%进度播放数
	Play50FeedBreak                       int64       `orm:"play_50_feed_break" json:"play50FeedBreak" dc:"50%进度播放数"`                                                  // 50%进度播放数
	Play75FeedBreak                       int64       `orm:"play_75_feed_break" json:"play75FeedBreak" dc:"75%进度播放数"`                                                  // 75%进度播放数
	Play99FeedBreak                       int64       `orm:"play_99_feed_break" json:"play99FeedBreak" dc:"99%进度播放数"`                                                  // 99%进度播放数
	AveragePlayTimePerPlay                float64     `orm:"average_play_time_per_play" json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`                                   // 平均单次播放时长
	PlayOverRate                          float64     `orm:"play_over_rate" json:"playOverRate" dc:"完播率"`                                                              // 完播率
	WifiPlayRate                          float64     `orm:"wifi_play_rate" json:"wifiPlayRate" dc:"WiFi播放占比"`                                                         // WiFi播放占比
	CardShow                              int64       `orm:"card_show" json:"cardShow" dc:"3秒卡片展现数"`                                                                   // 3秒卡片展现数
	DyLike                                int64       `orm:"dy_like" json:"dyLike" dc:"点赞数"`                                                                           // 点赞数
	DyComment                             int64       `orm:"dy_comment" json:"dyComment" dc:"评论量"`                                                                     // 评论量
	DyShare                               int64       `orm:"dy_share" json:"dyShare" dc:"分享量"`                                                                         // 分享量
	IesChallengeClick                     int64       `orm:"ies_challenge_click" json:"iesChallengeClick" dc:"挑战赛查看数"`                                                 // 挑战赛查看数
	IesMusicClick                         int64       `orm:"ies_music_click" json:"iesMusicClick" dc:"音乐查看数"`                                                          // 音乐查看数
	LocationClick                         int64       `orm:"location_click" json:"locationClick" dc:"POI点击数"`                                                          // POI点击数
	CustomerEffective                     int64       `orm:"customer_effective" json:"customerEffective" dc:"有效获客"`                                                    // 有效获客
	Wechat                                int64       `orm:"wechat" json:"wechat" dc:"微信复制"`                                                                           // 微信复制
	AttributionMicroGame0DLtv             float64     `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`                         // 小程序/小游戏当日LTV
	AttributionMicroGame3DLtv             float64     `orm:"attribution_micro_game_3d_ltv" json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`                      // 小程序/小游戏激活后三日LTV
	AttributionMicroGame7DLtv             float64     `orm:"attribution_micro_game_7d_ltv" json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`                      // 小程序/小游戏激活后七日LTV
	AttributionMicroGame0DRoi             float64     `orm:"attribution_micro_game_0d_roi" json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`                     // 小程序/小游戏当日广告变现ROI
	AttributionMicroGame3DRoi             float64     `orm:"attribution_micro_game_3d_roi" json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`                  // 小程序/小游戏激活后三日广告变现ROI
	AttributionMicroGame7DRoi             float64     `orm:"attribution_micro_game_7d_roi" json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`                  // 小程序/小游戏激活后七日广告变现ROI
	CreatedAt                             *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                    // 创建时间
	UpdatedAt                             *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                    // 更新时间
	CreateDate                            string      `orm:"create_date" json:"createDate" dc:"统计日期"`                                                                  // 统计日期
	TotalAmount                           float64     `orm:"total_amount" json:"totalAmount" dc:"总充值金额"`                                                               // 总充值金额
	TotalAdUp                             float64     `orm:"total_ad_up" json:"totalAdUp" dc:"广告收入"`                                                                   // 广告收入
	Register                              int         `orm:"register" json:"register" dc:"注册数"`                                                                        // 注册数
}

type AdPromotionMetricsDataListRes struct {
	Id                                    int         `json:"id" dc:"自增id主键"`
	AdvertiserId                          string      `json:"advertiserId" dc:"账户ID"`
	PromotionId                           string      `json:"promotionId" dc:"广告ID"`
	ProjectId                             string      `json:"projectId" dc:"项目ID"`
	StatCost                              float64     `json:"statCost" dc:"消耗"`
	ShowCnt                               int64       `json:"showCnt" dc:"展示数"`
	CpmPlatform                           float64     `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                              int64       `json:"clickCnt" dc:"点击数"`
	Ctr                                   float64     `json:"ctr" dc:"点击率"`
	CpcPlatform                           float64     `json:"cpcPlatform" dc:"平均点击单价(元)"`
	ConvertCnt                            int64       `json:"convertCnt" dc:"转化数"`
	ConversionCost                        float64     `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate                        float64     `json:"conversionRate" dc:"转化率"`
	DeepConvertCnt                        int64       `json:"deepConvertCnt" dc:"深度转化数"`
	DeepConvertCost                       float64     `json:"deepConvertCost" dc:"深度转化成本"`
	DeepConvertRate                       float64     `json:"deepConvertRate" dc:"深度转化率"`
	AttributionConvertCnt                 int64       `json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	AttributionConvertCost                float64     `json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64       `json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64     `json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64       `json:"preConvertCount" dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64     `json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64     `json:"preConvertRate" dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64       `json:"clickStartCnt" dc:"安卓下载开始数"`
	ClickStartCost                        float64     `json:"clickStartCost" dc:"安卓下载开始成本"`
	ClickStartRate                        float64     `json:"clickStartRate" dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64       `json:"downloadFinishCnt" dc:"安卓下载完成数"`
	DownloadFinishCost                    float64     `json:"downloadFinishCost" dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64     `json:"downloadFinishRate" dc:"安卓下载完成率"`
	InstallFinishCnt                      int64       `json:"installFinishCnt" dc:"安卓安装完成数"`
	InstallFinishCost                     float64     `json:"installFinishCost" dc:"安卓安装完成成本"`
	InstallFinishRate                     float64     `json:"installFinishRate" dc:"安卓安装完成率"`
	Active                                int64       `json:"active" dc:"激活数"`
	ActiveCost                            float64     `json:"activeCost" dc:"激活成本"`
	ActiveRate                            float64     `json:"activeRate" dc:"激活率"`
	ActiveRegisterCost                    float64     `json:"activeRegisterCost" dc:"注册成本"`
	ActiveRegisterRate                    float64     `json:"activeRegisterRate" dc:"注册率"`
	GameAddiction                         int64       `json:"gameAddiction" dc:"关键行为数"`
	GameAddictionCost                     float64     `json:"gameAddictionCost" dc:"关键行为成本"`
	GameAddictionRate                     float64     `json:"gameAddictionRate" dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64       `json:"attributionNextDayOpenCnt" dc:"次留数"`
	AttributionNextDayOpenCost            float64     `json:"attributionNextDayOpenCost" dc:"次留成本"`
	AttributionNextDayOpenRate            float64     `json:"attributionNextDayOpenRate" dc:"次留率"`
	NextDayOpen                           int64       `json:"nextDayOpen" dc:"次留回传数"`
	ActivePay                             int64       `json:"activePay" dc:"首次付费数"`
	ActivePayCost                         float64     `json:"activePayCost" dc:"首次付费成本"`
	ActivePayRate                         float64     `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                          int64       `json:"gamePayCount" dc:"付费次数"`
	GamePayCost                           float64     `json:"gamePayCost" dc:"付费成本"`
	AttributionGamePay7DCount             int64       `json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64     `json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64       `json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64       `json:"inAppUv" dc:"APP内访问"`
	InAppDetailUv                         int64       `json:"inAppDetailUv" dc:"APP内访问详情页"`
	InAppCart                             int64       `json:"inAppCart" dc:"APP内加入购物车"`
	InAppPay                              int64       `json:"inAppPay" dc:"APP内付费"`
	InAppOrder                            int64       `json:"inAppOrder" dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64     `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64     `json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64     `json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64     `json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64     `json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64     `json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64     `json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64     `json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64     `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64     `json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64     `json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64     `json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64     `json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64     `json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64     `json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64     `json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64       `json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64       `json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64     `json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64     `json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64     `json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64     `json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64       `json:"attributionRetention2DCnt" dc:"2日留存数"`
	AttributionRetention2DCost            float64     `json:"attributionRetention2DCost" dc:"2日留存成本"`
	AttributionRetention2DRate            float64     `json:"attributionRetention2DRate" dc:"2日留存率"`
	AttributionRetention3DCnt             int64       `json:"attributionRetention3DCnt" dc:"3日留存数"`
	AttributionRetention3DCost            float64     `json:"attributionRetention3DCost" dc:"3日留存成本"`
	AttributionRetention3DRate            float64     `json:"attributionRetention3DRate" dc:"3日留存率"`
	AttributionRetention4DCnt             int64       `json:"attributionRetention4DCnt" dc:"4日留存数"`
	AttributionRetention4DCost            float64     `json:"attributionRetention4DCost" dc:"4日留存成本"`
	AttributionRetention4DRate            float64     `json:"attributionRetention4DRate" dc:"4日留存率"`
	AttributionRetention5DCnt             int64       `json:"attributionRetention5DCnt" dc:"5日留存数"`
	AttributionRetention5DCost            float64     `json:"attributionRetention5DCost" dc:"5日留存成本"`
	AttributionRetention5DRate            float64     `json:"attributionRetention5DRate" dc:"5日留存率"`
	AttributionRetention6DCnt             int64       `json:"attributionRetention6DCnt" dc:"6日留存数"`
	AttributionRetention6DCost            float64     `json:"attributionRetention6DCost" dc:"6日留存成本"`
	AttributionRetention6DRate            float64     `json:"attributionRetention6DRate" dc:"6日留存率"`
	AttributionRetention7DCnt             int64       `json:"attributionRetention7DCnt" dc:"7日留存数"`
	AttributionRetention7DCost            float64     `json:"attributionRetention7DCost" dc:"7日留存成本"`
	AttributionRetention7DRate            float64     `json:"attributionRetention7DRate" dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64       `json:"attributionRetention7DSumCnt" dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64     `json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`
	TotalPlay                             int64       `json:"totalPlay" dc:"播放量"`
	ValidPlay                             int64       `json:"validPlay" dc:"有效播放数"`
	ValidPlayCost                         float64     `json:"validPlayCost" dc:"有效播放成本"`
	ValidPlayRate                         float64     `json:"validPlayRate" dc:"有效播放率"`
	Play25FeedBreak                       int64       `json:"play25FeedBreak" dc:"25%进度播放数"`
	Play50FeedBreak                       int64       `json:"play50FeedBreak" dc:"50%进度播放数"`
	Play75FeedBreak                       int64       `json:"play75FeedBreak" dc:"75%进度播放数"`
	Play99FeedBreak                       int64       `json:"play99FeedBreak" dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64     `json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	PlayOverRate                          float64     `json:"playOverRate" dc:"完播率"`
	WifiPlayRate                          float64     `json:"wifiPlayRate" dc:"WiFi播放占比"`
	CardShow                              int64       `json:"cardShow" dc:"3秒卡片展现数"`
	DyLike                                int64       `json:"dyLike" dc:"点赞数"`
	DyComment                             int64       `json:"dyComment" dc:"评论量"`
	DyShare                               int64       `json:"dyShare" dc:"分享量"`
	IesChallengeClick                     int64       `json:"iesChallengeClick" dc:"挑战赛查看数"`
	IesMusicClick                         int64       `json:"iesMusicClick" dc:"音乐查看数"`
	LocationClick                         int64       `json:"locationClick" dc:"POI点击数"`
	CustomerEffective                     int64       `json:"customerEffective" dc:"有效获客"`
	Wechat                                int64       `json:"wechat" dc:"微信复制"`
	AttributionMicroGame0DLtv             float64     `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64     `json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64     `json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64     `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64     `json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64     `json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreatedAt                             *gtime.Time `json:"createdAt" dc:"创建时间"`
	CreateDate                            string      `json:"createDate" dc:"统计日期"`
	TotalAmount                           float64     `json:"totalAmount" dc:"总充值金额"`
	TotalAdUp                             float64     `json:"totalAdUp" dc:"广告收入"`
	Register                              int         `json:"register" dc:"注册数"`
}

// AdPromotionMetricsDataSearchReq 分页请求参数
type AdPromotionMetricsDataSearchReq struct {
	comModel.PageReq
	Id                                    string `p:"id" dc:"自增id主键"`                                                                                                         //自增id主键
	AdvertiserId                          string `p:"advertiserId" dc:"账户ID"`                                                                                                 //账户ID
	PromotionId                           string `p:"promotionId" dc:"广告ID"`                                                                                                  //广告ID
	ProjectId                             string `p:"projectId" dc:"项目ID"`                                                                                                    //项目ID
	StatCost                              string `p:"statCost" v:"statCost@float#消耗需为浮点数" dc:"消耗"`                                                                            //消耗
	ShowCnt                               string `p:"showCnt" v:"showCnt@integer#展示数需为整数" dc:"展示数"`                                                                           //展示数
	CpmPlatform                           string `p:"cpmPlatform" v:"cpmPlatform@float#平均千次展现费用(元)需为浮点数" dc:"平均千次展现费用(元)"`                                                    //平均千次展现费用(元)
	ClickCnt                              string `p:"clickCnt" v:"clickCnt@integer#点击数需为整数" dc:"点击数"`                                                                         //点击数
	Ctr                                   string `p:"ctr" v:"ctr@float#点击率需为浮点数" dc:"点击率"`                                                                                    //点击率
	CpcPlatform                           string `p:"cpcPlatform" v:"cpcPlatform@float#平均点击单价(元)需为浮点数" dc:"平均点击单价(元)"`                                                        //平均点击单价(元)
	ConvertCnt                            string `p:"convertCnt" v:"convertCnt@integer#转化数需为整数" dc:"转化数"`                                                                     //转化数
	ConversionCost                        string `p:"conversionCost" v:"conversionCost@float#平均转化成本需为浮点数" dc:"平均转化成本"`                                                        //平均转化成本
	ConversionRate                        string `p:"conversionRate" v:"conversionRate@float#转化率需为浮点数" dc:"转化率"`                                                              //转化率
	DeepConvertCnt                        string `p:"deepConvertCnt" v:"deepConvertCnt@integer#深度转化数需为整数" dc:"深度转化数"`                                                         //深度转化数
	DeepConvertCost                       string `p:"deepConvertCost" v:"deepConvertCost@float#深度转化成本需为浮点数" dc:"深度转化成本"`                                                      //深度转化成本
	DeepConvertRate                       string `p:"deepConvertRate" v:"deepConvertRate@float#深度转化率需为浮点数" dc:"深度转化率"`                                                        //深度转化率
	AttributionConvertCnt                 string `p:"attributionConvertCnt" v:"attributionConvertCnt@integer#转化数(计费时间)需为整数" dc:"转化数(计费时间)"`                                   //转化数(计费时间)
	AttributionConvertCost                string `p:"attributionConvertCost" v:"attributionConvertCost@float#转化成本(计费时间)需为浮点数" dc:"转化成本(计费时间)"`                                //转化成本(计费时间)
	AttributionDeepConvertCnt             string `p:"attributionDeepConvertCnt" v:"attributionDeepConvertCnt@integer#深度转化数(计费时间)需为整数" dc:"深度转化数(计费时间)"`                       //深度转化数(计费时间)
	AttributionDeepConvertCost            string `p:"attributionDeepConvertCost" v:"attributionDeepConvertCost@float#深度转化成本(计费时间)需为浮点数" dc:"深度转化成本(计费时间)"`                    //深度转化成本(计费时间)
	PreConvertCount                       string `p:"preConvertCount" v:"preConvertCount@integer#预估转化数(计费时间)需为整数" dc:"预估转化数(计费时间)"`                                           //预估转化数(计费时间)
	PreConvertCost                        string `p:"preConvertCost" v:"preConvertCost@float#预估转化成本(计费时间)需为浮点数" dc:"预估转化成本(计费时间)"`                                            //预估转化成本(计费时间)
	PreConvertRate                        string `p:"preConvertRate" v:"preConvertRate@float#预估转化率(计费时间)需为浮点数" dc:"预估转化率(计费时间)"`                                              //预估转化率(计费时间)
	ClickStartCnt                         string `p:"clickStartCnt" v:"clickStartCnt@integer#安卓下载开始数需为整数" dc:"安卓下载开始数"`                                                       //安卓下载开始数
	ClickStartCost                        string `p:"clickStartCost" v:"clickStartCost@float#安卓下载开始成本需为浮点数" dc:"安卓下载开始成本"`                                                    //安卓下载开始成本
	ClickStartRate                        string `p:"clickStartRate" v:"clickStartRate@float#安卓下载开始率需为浮点数" dc:"安卓下载开始率"`                                                      //安卓下载开始率
	DownloadFinishCnt                     string `p:"downloadFinishCnt" v:"downloadFinishCnt@integer#安卓下载完成数需为整数" dc:"安卓下载完成数"`                                               //安卓下载完成数
	DownloadFinishCost                    string `p:"downloadFinishCost" v:"downloadFinishCost@float#安卓下载完成成本需为浮点数" dc:"安卓下载完成成本"`                                            //安卓下载完成成本
	DownloadFinishRate                    string `p:"downloadFinishRate" v:"downloadFinishRate@float#安卓下载完成率需为浮点数" dc:"安卓下载完成率"`                                              //安卓下载完成率
	InstallFinishCnt                      string `p:"installFinishCnt" v:"installFinishCnt@integer#安卓安装完成数需为整数" dc:"安卓安装完成数"`                                                 //安卓安装完成数
	InstallFinishCost                     string `p:"installFinishCost" v:"installFinishCost@float#安卓安装完成成本需为浮点数" dc:"安卓安装完成成本"`                                              //安卓安装完成成本
	InstallFinishRate                     string `p:"installFinishRate" v:"installFinishRate@float#安卓安装完成率需为浮点数" dc:"安卓安装完成率"`                                                //安卓安装完成率
	Active                                string `p:"active" v:"active@integer#激活数需为整数" dc:"激活数"`                                                                             //激活数
	ActiveCost                            string `p:"activeCost" v:"activeCost@float#激活成本需为浮点数" dc:"激活成本"`                                                                    //激活成本
	ActiveRate                            string `p:"activeRate" v:"activeRate@float#激活率需为浮点数" dc:"激活率"`                                                                      //激活率
	ActiveRegisterCost                    string `p:"activeRegisterCost" v:"activeRegisterCost@float#注册成本需为浮点数" dc:"注册成本"`                                                    //注册成本
	ActiveRegisterRate                    string `p:"activeRegisterRate" v:"activeRegisterRate@float#注册率需为浮点数" dc:"注册率"`                                                      //注册率
	GameAddiction                         string `p:"gameAddiction" v:"gameAddiction@integer#关键行为数需为整数" dc:"关键行为数"`                                                           //关键行为数
	GameAddictionCost                     string `p:"gameAddictionCost" v:"gameAddictionCost@float#关键行为成本需为浮点数" dc:"关键行为成本"`                                                  //关键行为成本
	GameAddictionRate                     string `p:"gameAddictionRate" v:"gameAddictionRate@float#关键行为率需为浮点数" dc:"关键行为率"`                                                    //关键行为率
	AttributionNextDayOpenCnt             string `p:"attributionNextDayOpenCnt" v:"attributionNextDayOpenCnt@integer#次留数需为整数" dc:"次留数"`                                       //次留数
	AttributionNextDayOpenCost            string `p:"attributionNextDayOpenCost" v:"attributionNextDayOpenCost@float#次留成本需为浮点数" dc:"次留成本"`                                    //次留成本
	AttributionNextDayOpenRate            string `p:"attributionNextDayOpenRate" v:"attributionNextDayOpenRate@float#次留率需为浮点数" dc:"次留率"`                                      //次留率
	NextDayOpen                           string `p:"nextDayOpen" v:"nextDayOpen@integer#次留回传数需为整数" dc:"次留回传数"`                                                               //次留回传数
	ActivePay                             string `p:"activePay" v:"activePay@integer#首次付费数需为整数" dc:"首次付费数"`                                                                   //首次付费数
	ActivePayCost                         string `p:"activePayCost" v:"activePayCost@float#首次付费成本需为浮点数" dc:"首次付费成本"`                                                          //首次付费成本
	ActivePayRate                         string `p:"activePayRate" v:"activePayRate@float#首次付费率需为浮点数" dc:"首次付费率"`                                                            //首次付费率
	GamePayCount                          string `p:"gamePayCount" v:"gamePayCount@integer#付费次数需为整数" dc:"付费次数"`                                                               //付费次数
	GamePayCost                           string `p:"gamePayCost" v:"gamePayCost@float#付费成本需为浮点数" dc:"付费成本"`                                                                  //付费成本
	AttributionGamePay7DCount             string `p:"attributionGamePay7DCount" v:"attributionGamePay7DCount@integer#7日付费次数(激活时间)需为整数" dc:"7日付费次数(激活时间)"`                     //7日付费次数(激活时间)
	AttributionGamePay7DCost              string `p:"attributionGamePay7DCost" v:"attributionGamePay7DCost@float#7日付费成本(激活时间)需为浮点数" dc:"7日付费成本(激活时间)"`                        //7日付费成本(激活时间)
	AttributionActivePay7DPerCount        string `p:"attributionActivePay7DPerCount" v:"attributionActivePay7DPerCount@integer#7日人均付费次数(激活时间)需为整数" dc:"7日人均付费次数(激活时间)"`       //7日人均付费次数(激活时间)
	InAppUv                               string `p:"inAppUv" v:"inAppUv@integer#APP内访问需为整数" dc:"APP内访问"`                                                                     //APP内访问
	InAppDetailUv                         string `p:"inAppDetailUv" v:"inAppDetailUv@integer#APP内访问详情页需为整数" dc:"APP内访问详情页"`                                                   //APP内访问详情页
	InAppCart                             string `p:"inAppCart" v:"inAppCart@integer#APP内加入购物车需为整数" dc:"APP内加入购物车"`                                                           //APP内加入购物车
	InAppPay                              string `p:"inAppPay" v:"inAppPay@integer#APP内付费需为整数" dc:"APP内付费"`                                                                   //APP内付费
	InAppOrder                            string `p:"inAppOrder" v:"inAppOrder@integer#APP内下单需为整数" dc:"APP内下单"`                                                               //APP内下单
	AttributionGameInAppLtv1Day           string `p:"attributionGameInAppLtv1Day" v:"attributionGameInAppLtv1Day@float#当日付费金额需为浮点数" dc:"当日付费金额"`                              //当日付费金额
	AttributionGameInAppLtv2Days          string `p:"attributionGameInAppLtv2Days" v:"attributionGameInAppLtv2Days@float#激活后一日付费金额需为浮点数" dc:"激活后一日付费金额"`                      //激活后一日付费金额
	AttributionGameInAppLtv3Days          string `p:"attributionGameInAppLtv3Days" v:"attributionGameInAppLtv3Days@float#激活后二日付费金额需为浮点数" dc:"激活后二日付费金额"`                      //激活后二日付费金额
	AttributionGameInAppLtv4Days          string `p:"attributionGameInAppLtv4Days" v:"attributionGameInAppLtv4Days@float#激活后三日付费金额需为浮点数" dc:"激活后三日付费金额"`                      //激活后三日付费金额
	AttributionGameInAppLtv5Days          string `p:"attributionGameInAppLtv5Days" v:"attributionGameInAppLtv5Days@float#激活后四日付费金额需为浮点数" dc:"激活后四日付费金额"`                      //激活后四日付费金额
	AttributionGameInAppLtv6Days          string `p:"attributionGameInAppLtv6Days" v:"attributionGameInAppLtv6Days@float#激活后五日付费金额需为浮点数" dc:"激活后五日付费金额"`                      //激活后五日付费金额
	AttributionGameInAppLtv7Days          string `p:"attributionGameInAppLtv7Days" v:"attributionGameInAppLtv7Days@float#激活后六日付费金额需为浮点数" dc:"激活后六日付费金额"`                      //激活后六日付费金额
	AttributionGameInAppLtv8Days          string `p:"attributionGameInAppLtv8Days" v:"attributionGameInAppLtv8Days@float#激活后七日付费金额需为浮点数" dc:"激活后七日付费金额"`                      //激活后七日付费金额
	AttributionGameInAppRoi1Day           string `p:"attributionGameInAppRoi1Day" v:"attributionGameInAppRoi1Day@float#当日付费ROI需为浮点数" dc:"当日付费ROI"`                            //当日付费ROI
	AttributionGameInAppRoi2Days          string `p:"attributionGameInAppRoi2Days" v:"attributionGameInAppRoi2Days@float#激活后一日付费ROI需为浮点数" dc:"激活后一日付费ROI"`                    //激活后一日付费ROI
	AttributionGameInAppRoi3Days          string `p:"attributionGameInAppRoi3Days" v:"attributionGameInAppRoi3Days@float#激活后二日付费ROI需为浮点数" dc:"激活后二日付费ROI"`                    //激活后二日付费ROI
	AttributionGameInAppRoi4Days          string `p:"attributionGameInAppRoi4Days" v:"attributionGameInAppRoi4Days@float#激活后三日付费ROI需为浮点数" dc:"激活后三日付费ROI"`                    //激活后三日付费ROI
	AttributionGameInAppRoi5Days          string `p:"attributionGameInAppRoi5Days" v:"attributionGameInAppRoi5Days@float#激活后四日付费ROI需为浮点数" dc:"激活后四日付费ROI"`                    //激活后四日付费ROI
	AttributionGameInAppRoi6Days          string `p:"attributionGameInAppRoi6Days" v:"attributionGameInAppRoi6Days@float#激活后五日付费ROI需为浮点数" dc:"激活后五日付费ROI"`                    //激活后五日付费ROI
	AttributionGameInAppRoi7Days          string `p:"attributionGameInAppRoi7Days" v:"attributionGameInAppRoi7Days@float#激活后六日付费ROI需为浮点数" dc:"激活后六日付费ROI"`                    //激活后六日付费ROI
	AttributionGameInAppRoi8Days          string `p:"attributionGameInAppRoi8Days" v:"attributionGameInAppRoi8Days@float#激活后七日付费ROI需为浮点数" dc:"激活后七日付费ROI"`                    //激活后七日付费ROI
	AttributionDayActivePayCount          string `p:"attributionDayActivePayCount" v:"attributionDayActivePayCount@integer#计费当日激活且首次付费数需为整数" dc:"计费当日激活且首次付费数"`               //计费当日激活且首次付费数
	AttributionActivePayIntraOneDayCount  string `p:"attributionActivePayIntraOneDayCount" v:"attributionActivePayIntraOneDayCount@integer#激活后24h首次付费数需为整数" dc:"激活后24h首次付费数"` //激活后24h首次付费数
	AttributionActivePayIntraOneDayCost   string `p:"attributionActivePayIntraOneDayCost" v:"attributionActivePayIntraOneDayCost@float#激活后24h首次付费成本需为浮点数" dc:"激活后24h首次付费成本"`  //激活后24h首次付费成本
	AttributionActivePayIntraOneDayRate   string `p:"attributionActivePayIntraOneDayRate" v:"attributionActivePayIntraOneDayRate@float#激活后24h首次付费率需为浮点数" dc:"激活后24h首次付费率"`    //激活后24h首次付费率
	AttributionActivePayIntraOneDayAmount string `p:"attributionActivePayIntraOneDayAmount" v:"attributionActivePayIntraOneDayAmount@float#激活后24h付费金额需为浮点数" dc:"激活后24h付费金额"`  //激活后24h付费金额
	AttributionActivePayIntraOneDayRoi    string `p:"attributionActivePayIntraOneDayRoi" v:"attributionActivePayIntraOneDayRoi@float#激活后24h付费ROI需为浮点数" dc:"激活后24h付费ROI"`      //激活后24h付费ROI
	AttributionRetention2DCnt             string `p:"attributionRetention2DCnt" v:"attributionRetention2DCnt@integer#2日留存数需为整数" dc:"2日留存数"`                                   //2日留存数
	AttributionRetention2DCost            string `p:"attributionRetention2DCost" v:"attributionRetention2DCost@float#2日留存成本需为浮点数" dc:"2日留存成本"`                                //2日留存成本
	AttributionRetention2DRate            string `p:"attributionRetention2DRate" v:"attributionRetention2DRate@float#2日留存率需为浮点数" dc:"2日留存率"`                                  //2日留存率
	AttributionRetention3DCnt             string `p:"attributionRetention3DCnt" v:"attributionRetention3DCnt@integer#3日留存数需为整数" dc:"3日留存数"`                                   //3日留存数
	AttributionRetention3DCost            string `p:"attributionRetention3DCost" v:"attributionRetention3DCost@float#3日留存成本需为浮点数" dc:"3日留存成本"`                                //3日留存成本
	AttributionRetention3DRate            string `p:"attributionRetention3DRate" v:"attributionRetention3DRate@float#3日留存率需为浮点数" dc:"3日留存率"`                                  //3日留存率
	AttributionRetention4DCnt             string `p:"attributionRetention4DCnt" v:"attributionRetention4DCnt@integer#4日留存数需为整数" dc:"4日留存数"`                                   //4日留存数
	AttributionRetention4DCost            string `p:"attributionRetention4DCost" v:"attributionRetention4DCost@float#4日留存成本需为浮点数" dc:"4日留存成本"`                                //4日留存成本
	AttributionRetention4DRate            string `p:"attributionRetention4DRate" v:"attributionRetention4DRate@float#4日留存率需为浮点数" dc:"4日留存率"`                                  //4日留存率
	AttributionRetention5DCnt             string `p:"attributionRetention5DCnt" v:"attributionRetention5DCnt@integer#5日留存数需为整数" dc:"5日留存数"`                                   //5日留存数
	AttributionRetention5DCost            string `p:"attributionRetention5DCost" v:"attributionRetention5DCost@float#5日留存成本需为浮点数" dc:"5日留存成本"`                                //5日留存成本
	AttributionRetention5DRate            string `p:"attributionRetention5DRate" v:"attributionRetention5DRate@float#5日留存率需为浮点数" dc:"5日留存率"`                                  //5日留存率
	AttributionRetention6DCnt             string `p:"attributionRetention6DCnt" v:"attributionRetention6DCnt@integer#6日留存数需为整数" dc:"6日留存数"`                                   //6日留存数
	AttributionRetention6DCost            string `p:"attributionRetention6DCost" v:"attributionRetention6DCost@float#6日留存成本需为浮点数" dc:"6日留存成本"`                                //6日留存成本
	AttributionRetention6DRate            string `p:"attributionRetention6DRate" v:"attributionRetention6DRate@float#6日留存率需为浮点数" dc:"6日留存率"`                                  //6日留存率
	AttributionRetention7DCnt             string `p:"attributionRetention7DCnt" v:"attributionRetention7DCnt@integer#7日留存数需为整数" dc:"7日留存数"`                                   //7日留存数
	AttributionRetention7DCost            string `p:"attributionRetention7DCost" v:"attributionRetention7DCost@float#7日留存成本需为浮点数" dc:"7日留存成本"`                                //7日留存成本
	AttributionRetention7DRate            string `p:"attributionRetention7DRate" v:"attributionRetention7DRate@float#7日留存率需为浮点数" dc:"7日留存率"`                                  //7日留存率
	AttributionRetention7DSumCnt          string `p:"attributionRetention7DSumCnt" v:"attributionRetention7DSumCnt@integer#7日留存总数需为整数" dc:"7日留存总数"`                           //7日留存总数
	AttributionRetention7DTotalCost       string `p:"attributionRetention7DTotalCost" v:"attributionRetention7DTotalCost@float#7日留存总成本需为浮点数" dc:"7日留存总成本"`                    //7日留存总成本
	TotalPlay                             string `p:"totalPlay" v:"totalPlay@integer#播放量需为整数" dc:"播放量"`                                                                       //播放量
	ValidPlay                             string `p:"validPlay" v:"validPlay@integer#有效播放数需为整数" dc:"有效播放数"`                                                                   //有效播放数
	ValidPlayCost                         string `p:"validPlayCost" v:"validPlayCost@float#有效播放成本需为浮点数" dc:"有效播放成本"`                                                          //有效播放成本
	ValidPlayRate                         string `p:"validPlayRate" v:"validPlayRate@float#有效播放率需为浮点数" dc:"有效播放率"`                                                            //有效播放率
	Play25FeedBreak                       string `p:"play25FeedBreak" v:"play25FeedBreak@integer#25%进度播放数需为整数" dc:"25%进度播放数"`                                                 //25%进度播放数
	Play50FeedBreak                       string `p:"play50FeedBreak" v:"play50FeedBreak@integer#50%进度播放数需为整数" dc:"50%进度播放数"`                                                 //50%进度播放数
	Play75FeedBreak                       string `p:"play75FeedBreak" v:"play75FeedBreak@integer#75%进度播放数需为整数" dc:"75%进度播放数"`                                                 //75%进度播放数
	Play99FeedBreak                       string `p:"play99FeedBreak" v:"play99FeedBreak@integer#99%进度播放数需为整数" dc:"99%进度播放数"`                                                 //99%进度播放数
	AveragePlayTimePerPlay                string `p:"averagePlayTimePerPlay" v:"averagePlayTimePerPlay@float#平均单次播放时长需为浮点数" dc:"平均单次播放时长"`                                    //平均单次播放时长
	PlayOverRate                          string `p:"playOverRate" v:"playOverRate@float#完播率需为浮点数" dc:"完播率"`                                                                  //完播率
	WifiPlayRate                          string `p:"wifiPlayRate" v:"wifiPlayRate@float#WiFi播放占比需为浮点数" dc:"WiFi播放占比"`                                                        //WiFi播放占比
	CardShow                              string `p:"cardShow" v:"cardShow@integer#3秒卡片展现数需为整数" dc:"3秒卡片展现数"`                                                                 //3秒卡片展现数
	DyLike                                string `p:"dyLike" v:"dyLike@integer#点赞数需为整数" dc:"点赞数"`                                                                             //点赞数
	DyComment                             string `p:"dyComment" v:"dyComment@integer#评论量需为整数" dc:"评论量"`                                                                       //评论量
	DyShare                               string `p:"dyShare" v:"dyShare@integer#分享量需为整数" dc:"分享量"`                                                                           //分享量
	IesChallengeClick                     string `p:"iesChallengeClick" v:"iesChallengeClick@integer#挑战赛查看数需为整数" dc:"挑战赛查看数"`                                                 //挑战赛查看数
	IesMusicClick                         string `p:"iesMusicClick" v:"iesMusicClick@integer#音乐查看数需为整数" dc:"音乐查看数"`                                                           //音乐查看数
	LocationClick                         string `p:"locationClick" v:"locationClick@integer#POI点击数需为整数" dc:"POI点击数"`                                                         //POI点击数
	CustomerEffective                     string `p:"customerEffective" v:"customerEffective@integer#有效获客需为整数" dc:"有效获客"`                                                     //有效获客
	Wechat                                string `p:"wechat" v:"wechat@integer#微信复制需为整数" dc:"微信复制"`                                                                           //微信复制
	AttributionMicroGame0DLtv             string `p:"attributionMicroGame0DLtv" v:"attributionMicroGame0DLtv@float#小程序/小游戏当日LTV需为浮点数" dc:"小程序/小游戏当日LTV"`                      //小程序/小游戏当日LTV
	AttributionMicroGame3DLtv             string `p:"attributionMicroGame3DLtv" v:"attributionMicroGame3DLtv@float#小程序/小游戏激活后三日LTV需为浮点数" dc:"小程序/小游戏激活后三日LTV"`                //小程序/小游戏激活后三日LTV
	AttributionMicroGame7DLtv             string `p:"attributionMicroGame7DLtv" v:"attributionMicroGame7DLtv@float#小程序/小游戏激活后七日LTV需为浮点数" dc:"小程序/小游戏激活后七日LTV"`                //小程序/小游戏激活后七日LTV
	AttributionMicroGame0DRoi             string `p:"attributionMicroGame0DRoi" v:"attributionMicroGame0DRoi@float#小程序/小游戏当日广告变现ROI需为浮点数" dc:"小程序/小游戏当日广告变现ROI"`              //小程序/小游戏当日广告变现ROI
	AttributionMicroGame3DRoi             string `p:"attributionMicroGame3DRoi" v:"attributionMicroGame3DRoi@float#小程序/小游戏激活后三日广告变现ROI需为浮点数" dc:"小程序/小游戏激活后三日广告变现ROI"`        //小程序/小游戏激活后三日广告变现ROI
	AttributionMicroGame7DRoi             string `p:"attributionMicroGame7DRoi" v:"attributionMicroGame7DRoi@float#小程序/小游戏激活后七日广告变现ROI需为浮点数" dc:"小程序/小游戏激活后七日广告变现ROI"`        //小程序/小游戏激活后七日广告变现ROI
	CreatedAt                             string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                 //创建时间
	CreateDate                            string `p:"createDate" dc:"统计日期"`                                                                                                   //统计日期
	TotalAmount                           string `p:"totalAmount" v:"totalAmount@float#总充值金额需为浮点数" dc:"总充值金额"`                                                                //总充值金额
	TotalAdUp                             string `p:"totalAdUp" v:"totalAdUp@float#广告收入需为浮点数" dc:"广告收入"`                                                                      //广告收入
	Register                              string `p:"register" v:"register@integer#注册数需为整数" dc:"注册数"`                                                                         //注册数
}

// AdPromotionMetricsDataSearchRes 列表返回结果
type AdPromotionMetricsDataSearchRes struct {
	comModel.ListRes
	List []*AdPromotionMetricsDataListRes `json:"list"`
}

// AdPromotionMetricsDataAddReq 添加操作请求参数
type AdPromotionMetricsDataAddReq struct {
	AdvertiserId                          string  `p:"advertiserId"  dc:"账户ID"`
	PromotionId                           string  `p:"promotionId"  dc:"广告ID"`
	ProjectId                             string  `p:"projectId"  dc:"项目ID"`
	StatCost                              float64 `p:"statCost"  dc:"消耗"`
	ShowCnt                               int64   `p:"showCnt"  dc:"展示数"`
	CpmPlatform                           float64 `p:"cpmPlatform"  dc:"平均千次展现费用(元)"`
	ClickCnt                              int64   `p:"clickCnt"  dc:"点击数"`
	Ctr                                   float64 `p:"ctr"  dc:"点击率"`
	CpcPlatform                           float64 `p:"cpcPlatform"  dc:"平均点击单价(元)"`
	ConvertCnt                            int64   `p:"convertCnt"  dc:"转化数"`
	ConversionCost                        float64 `p:"conversionCost"  dc:"平均转化成本"`
	ConversionRate                        float64 `p:"conversionRate"  dc:"转化率"`
	DeepConvertCnt                        int64   `p:"deepConvertCnt"  dc:"深度转化数"`
	DeepConvertCost                       float64 `p:"deepConvertCost"  dc:"深度转化成本"`
	DeepConvertRate                       float64 `p:"deepConvertRate"  dc:"深度转化率"`
	AttributionConvertCnt                 int64   `p:"attributionConvertCnt"  dc:"转化数(计费时间)"`
	AttributionConvertCost                float64 `p:"attributionConvertCost"  dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64   `p:"attributionDeepConvertCnt"  dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64 `p:"attributionDeepConvertCost"  dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64   `p:"preConvertCount"  dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64 `p:"preConvertCost"  dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64 `p:"preConvertRate"  dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64   `p:"clickStartCnt"  dc:"安卓下载开始数"`
	ClickStartCost                        float64 `p:"clickStartCost"  dc:"安卓下载开始成本"`
	ClickStartRate                        float64 `p:"clickStartRate"  dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64   `p:"downloadFinishCnt"  dc:"安卓下载完成数"`
	DownloadFinishCost                    float64 `p:"downloadFinishCost"  dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64 `p:"downloadFinishRate"  dc:"安卓下载完成率"`
	InstallFinishCnt                      int64   `p:"installFinishCnt"  dc:"安卓安装完成数"`
	InstallFinishCost                     float64 `p:"installFinishCost"  dc:"安卓安装完成成本"`
	InstallFinishRate                     float64 `p:"installFinishRate"  dc:"安卓安装完成率"`
	Active                                int64   `p:"active"  dc:"激活数"`
	ActiveCost                            float64 `p:"activeCost"  dc:"激活成本"`
	ActiveRate                            float64 `p:"activeRate"  dc:"激活率"`
	ActiveRegisterCost                    float64 `p:"activeRegisterCost"  dc:"注册成本"`
	ActiveRegisterRate                    float64 `p:"activeRegisterRate"  dc:"注册率"`
	GameAddiction                         int64   `p:"gameAddiction"  dc:"关键行为数"`
	GameAddictionCost                     float64 `p:"gameAddictionCost"  dc:"关键行为成本"`
	GameAddictionRate                     float64 `p:"gameAddictionRate"  dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64   `p:"attributionNextDayOpenCnt"  dc:"次留数"`
	AttributionNextDayOpenCost            float64 `p:"attributionNextDayOpenCost"  dc:"次留成本"`
	AttributionNextDayOpenRate            float64 `p:"attributionNextDayOpenRate"  dc:"次留率"`
	NextDayOpen                           int64   `p:"nextDayOpen"  dc:"次留回传数"`
	ActivePay                             int64   `p:"activePay"  dc:"首次付费数"`
	ActivePayCost                         float64 `p:"activePayCost"  dc:"首次付费成本"`
	ActivePayRate                         float64 `p:"activePayRate"  dc:"首次付费率"`
	GamePayCount                          int64   `p:"gamePayCount"  dc:"付费次数"`
	GamePayCost                           float64 `p:"gamePayCost"  dc:"付费成本"`
	AttributionGamePay7DCount             int64   `p:"attributionGamePay7DCount"  dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64 `p:"attributionGamePay7DCost"  dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64   `p:"attributionActivePay7DPerCount"  dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64   `p:"inAppUv"  dc:"APP内访问"`
	InAppDetailUv                         int64   `p:"inAppDetailUv"  dc:"APP内访问详情页"`
	InAppCart                             int64   `p:"inAppCart"  dc:"APP内加入购物车"`
	InAppPay                              int64   `p:"inAppPay"  dc:"APP内付费"`
	InAppOrder                            int64   `p:"inAppOrder"  dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64 `p:"attributionGameInAppLtv1Day"  dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64 `p:"attributionGameInAppLtv2Days"  dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64 `p:"attributionGameInAppLtv3Days"  dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64 `p:"attributionGameInAppLtv4Days"  dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64 `p:"attributionGameInAppLtv5Days"  dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64 `p:"attributionGameInAppLtv6Days"  dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64 `p:"attributionGameInAppLtv7Days"  dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64 `p:"attributionGameInAppLtv8Days"  dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64 `p:"attributionGameInAppRoi1Day"  dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64 `p:"attributionGameInAppRoi2Days"  dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64 `p:"attributionGameInAppRoi3Days"  dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64 `p:"attributionGameInAppRoi4Days"  dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64 `p:"attributionGameInAppRoi5Days"  dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64 `p:"attributionGameInAppRoi6Days"  dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64 `p:"attributionGameInAppRoi7Days"  dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64 `p:"attributionGameInAppRoi8Days"  dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64   `p:"attributionDayActivePayCount"  dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64   `p:"attributionActivePayIntraOneDayCount"  dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64 `p:"attributionActivePayIntraOneDayCost"  dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64 `p:"attributionActivePayIntraOneDayRate"  dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64 `p:"attributionActivePayIntraOneDayAmount"  dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64 `p:"attributionActivePayIntraOneDayRoi"  dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64   `p:"attributionRetention2DCnt"  dc:"2日留存数"`
	AttributionRetention2DCost            float64 `p:"attributionRetention2DCost"  dc:"2日留存成本"`
	AttributionRetention2DRate            float64 `p:"attributionRetention2DRate"  dc:"2日留存率"`
	AttributionRetention3DCnt             int64   `p:"attributionRetention3DCnt"  dc:"3日留存数"`
	AttributionRetention3DCost            float64 `p:"attributionRetention3DCost"  dc:"3日留存成本"`
	AttributionRetention3DRate            float64 `p:"attributionRetention3DRate"  dc:"3日留存率"`
	AttributionRetention4DCnt             int64   `p:"attributionRetention4DCnt"  dc:"4日留存数"`
	AttributionRetention4DCost            float64 `p:"attributionRetention4DCost"  dc:"4日留存成本"`
	AttributionRetention4DRate            float64 `p:"attributionRetention4DRate"  dc:"4日留存率"`
	AttributionRetention5DCnt             int64   `p:"attributionRetention5DCnt"  dc:"5日留存数"`
	AttributionRetention5DCost            float64 `p:"attributionRetention5DCost"  dc:"5日留存成本"`
	AttributionRetention5DRate            float64 `p:"attributionRetention5DRate"  dc:"5日留存率"`
	AttributionRetention6DCnt             int64   `p:"attributionRetention6DCnt"  dc:"6日留存数"`
	AttributionRetention6DCost            float64 `p:"attributionRetention6DCost"  dc:"6日留存成本"`
	AttributionRetention6DRate            float64 `p:"attributionRetention6DRate"  dc:"6日留存率"`
	AttributionRetention7DCnt             int64   `p:"attributionRetention7DCnt"  dc:"7日留存数"`
	AttributionRetention7DCost            float64 `p:"attributionRetention7DCost"  dc:"7日留存成本"`
	AttributionRetention7DRate            float64 `p:"attributionRetention7DRate"  dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64   `p:"attributionRetention7DSumCnt"  dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64 `p:"attributionRetention7DTotalCost"  dc:"7日留存总成本"`
	TotalPlay                             int64   `p:"totalPlay"  dc:"播放量"`
	ValidPlay                             int64   `p:"validPlay"  dc:"有效播放数"`
	ValidPlayCost                         float64 `p:"validPlayCost"  dc:"有效播放成本"`
	ValidPlayRate                         float64 `p:"validPlayRate"  dc:"有效播放率"`
	Play25FeedBreak                       int64   `p:"play25FeedBreak"  dc:"25%进度播放数"`
	Play50FeedBreak                       int64   `p:"play50FeedBreak"  dc:"50%进度播放数"`
	Play75FeedBreak                       int64   `p:"play75FeedBreak"  dc:"75%进度播放数"`
	Play99FeedBreak                       int64   `p:"play99FeedBreak"  dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64 `p:"averagePlayTimePerPlay"  dc:"平均单次播放时长"`
	PlayOverRate                          float64 `p:"playOverRate"  dc:"完播率"`
	WifiPlayRate                          float64 `p:"wifiPlayRate"  dc:"WiFi播放占比"`
	CardShow                              int64   `p:"cardShow"  dc:"3秒卡片展现数"`
	DyLike                                int64   `p:"dyLike"  dc:"点赞数"`
	DyComment                             int64   `p:"dyComment"  dc:"评论量"`
	DyShare                               int64   `p:"dyShare"  dc:"分享量"`
	IesChallengeClick                     int64   `p:"iesChallengeClick"  dc:"挑战赛查看数"`
	IesMusicClick                         int64   `p:"iesMusicClick"  dc:"音乐查看数"`
	LocationClick                         int64   `p:"locationClick"  dc:"POI点击数"`
	CustomerEffective                     int64   `p:"customerEffective"  dc:"有效获客"`
	Wechat                                int64   `p:"wechat"  dc:"微信复制"`
	AttributionMicroGame0DLtv             float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64 `p:"attributionMicroGame3DLtv"  dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64 `p:"attributionMicroGame7DLtv"  dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64 `p:"attributionMicroGame0DRoi"  dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64 `p:"attributionMicroGame3DRoi"  dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64 `p:"attributionMicroGame7DRoi"  dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreateDate                            string  `p:"createDate"  dc:"统计日期"`
	TotalAmount                           float64 `p:"totalAmount"  dc:"总充值金额"`
	TotalAdUp                             float64 `p:"totalAdUp"  dc:"广告收入"`
	Register                              int     `p:"register"  dc:"注册数"`
}

// AdPromotionMetricsDataEditReq 修改操作请求参数
type AdPromotionMetricsDataEditReq struct {
	Id                                    int     `p:"id" v:"required#主键ID不能为空" dc:"自增id主键"`
	AdvertiserId                          string  `p:"advertiserId"  dc:"账户ID"`
	PromotionId                           string  `p:"promotionId"  dc:"广告ID"`
	ProjectId                             string  `p:"projectId"  dc:"项目ID"`
	StatCost                              float64 `p:"statCost"  dc:"消耗"`
	ShowCnt                               int64   `p:"showCnt"  dc:"展示数"`
	CpmPlatform                           float64 `p:"cpmPlatform"  dc:"平均千次展现费用(元)"`
	ClickCnt                              int64   `p:"clickCnt"  dc:"点击数"`
	Ctr                                   float64 `p:"ctr"  dc:"点击率"`
	CpcPlatform                           float64 `p:"cpcPlatform"  dc:"平均点击单价(元)"`
	ConvertCnt                            int64   `p:"convertCnt"  dc:"转化数"`
	ConversionCost                        float64 `p:"conversionCost"  dc:"平均转化成本"`
	ConversionRate                        float64 `p:"conversionRate"  dc:"转化率"`
	DeepConvertCnt                        int64   `p:"deepConvertCnt"  dc:"深度转化数"`
	DeepConvertCost                       float64 `p:"deepConvertCost"  dc:"深度转化成本"`
	DeepConvertRate                       float64 `p:"deepConvertRate"  dc:"深度转化率"`
	AttributionConvertCnt                 int64   `p:"attributionConvertCnt"  dc:"转化数(计费时间)"`
	AttributionConvertCost                float64 `p:"attributionConvertCost"  dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64   `p:"attributionDeepConvertCnt"  dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64 `p:"attributionDeepConvertCost"  dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64   `p:"preConvertCount"  dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64 `p:"preConvertCost"  dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64 `p:"preConvertRate"  dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64   `p:"clickStartCnt"  dc:"安卓下载开始数"`
	ClickStartCost                        float64 `p:"clickStartCost"  dc:"安卓下载开始成本"`
	ClickStartRate                        float64 `p:"clickStartRate"  dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64   `p:"downloadFinishCnt"  dc:"安卓下载完成数"`
	DownloadFinishCost                    float64 `p:"downloadFinishCost"  dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64 `p:"downloadFinishRate"  dc:"安卓下载完成率"`
	InstallFinishCnt                      int64   `p:"installFinishCnt"  dc:"安卓安装完成数"`
	InstallFinishCost                     float64 `p:"installFinishCost"  dc:"安卓安装完成成本"`
	InstallFinishRate                     float64 `p:"installFinishRate"  dc:"安卓安装完成率"`
	Active                                int64   `p:"active"  dc:"激活数"`
	ActiveCost                            float64 `p:"activeCost"  dc:"激活成本"`
	ActiveRate                            float64 `p:"activeRate"  dc:"激活率"`
	ActiveRegisterCost                    float64 `p:"activeRegisterCost"  dc:"注册成本"`
	ActiveRegisterRate                    float64 `p:"activeRegisterRate"  dc:"注册率"`
	GameAddiction                         int64   `p:"gameAddiction"  dc:"关键行为数"`
	GameAddictionCost                     float64 `p:"gameAddictionCost"  dc:"关键行为成本"`
	GameAddictionRate                     float64 `p:"gameAddictionRate"  dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64   `p:"attributionNextDayOpenCnt"  dc:"次留数"`
	AttributionNextDayOpenCost            float64 `p:"attributionNextDayOpenCost"  dc:"次留成本"`
	AttributionNextDayOpenRate            float64 `p:"attributionNextDayOpenRate"  dc:"次留率"`
	NextDayOpen                           int64   `p:"nextDayOpen"  dc:"次留回传数"`
	ActivePay                             int64   `p:"activePay"  dc:"首次付费数"`
	ActivePayCost                         float64 `p:"activePayCost"  dc:"首次付费成本"`
	ActivePayRate                         float64 `p:"activePayRate"  dc:"首次付费率"`
	GamePayCount                          int64   `p:"gamePayCount"  dc:"付费次数"`
	GamePayCost                           float64 `p:"gamePayCost"  dc:"付费成本"`
	AttributionGamePay7DCount             int64   `p:"attributionGamePay7DCount"  dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64 `p:"attributionGamePay7DCost"  dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64   `p:"attributionActivePay7DPerCount"  dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64   `p:"inAppUv"  dc:"APP内访问"`
	InAppDetailUv                         int64   `p:"inAppDetailUv"  dc:"APP内访问详情页"`
	InAppCart                             int64   `p:"inAppCart"  dc:"APP内加入购物车"`
	InAppPay                              int64   `p:"inAppPay"  dc:"APP内付费"`
	InAppOrder                            int64   `p:"inAppOrder"  dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64 `p:"attributionGameInAppLtv1Day"  dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64 `p:"attributionGameInAppLtv2Days"  dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64 `p:"attributionGameInAppLtv3Days"  dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64 `p:"attributionGameInAppLtv4Days"  dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64 `p:"attributionGameInAppLtv5Days"  dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64 `p:"attributionGameInAppLtv6Days"  dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64 `p:"attributionGameInAppLtv7Days"  dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64 `p:"attributionGameInAppLtv8Days"  dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64 `p:"attributionGameInAppRoi1Day"  dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64 `p:"attributionGameInAppRoi2Days"  dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64 `p:"attributionGameInAppRoi3Days"  dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64 `p:"attributionGameInAppRoi4Days"  dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64 `p:"attributionGameInAppRoi5Days"  dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64 `p:"attributionGameInAppRoi6Days"  dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64 `p:"attributionGameInAppRoi7Days"  dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64 `p:"attributionGameInAppRoi8Days"  dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64   `p:"attributionDayActivePayCount"  dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64   `p:"attributionActivePayIntraOneDayCount"  dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64 `p:"attributionActivePayIntraOneDayCost"  dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64 `p:"attributionActivePayIntraOneDayRate"  dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64 `p:"attributionActivePayIntraOneDayAmount"  dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64 `p:"attributionActivePayIntraOneDayRoi"  dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64   `p:"attributionRetention2DCnt"  dc:"2日留存数"`
	AttributionRetention2DCost            float64 `p:"attributionRetention2DCost"  dc:"2日留存成本"`
	AttributionRetention2DRate            float64 `p:"attributionRetention2DRate"  dc:"2日留存率"`
	AttributionRetention3DCnt             int64   `p:"attributionRetention3DCnt"  dc:"3日留存数"`
	AttributionRetention3DCost            float64 `p:"attributionRetention3DCost"  dc:"3日留存成本"`
	AttributionRetention3DRate            float64 `p:"attributionRetention3DRate"  dc:"3日留存率"`
	AttributionRetention4DCnt             int64   `p:"attributionRetention4DCnt"  dc:"4日留存数"`
	AttributionRetention4DCost            float64 `p:"attributionRetention4DCost"  dc:"4日留存成本"`
	AttributionRetention4DRate            float64 `p:"attributionRetention4DRate"  dc:"4日留存率"`
	AttributionRetention5DCnt             int64   `p:"attributionRetention5DCnt"  dc:"5日留存数"`
	AttributionRetention5DCost            float64 `p:"attributionRetention5DCost"  dc:"5日留存成本"`
	AttributionRetention5DRate            float64 `p:"attributionRetention5DRate"  dc:"5日留存率"`
	AttributionRetention6DCnt             int64   `p:"attributionRetention6DCnt"  dc:"6日留存数"`
	AttributionRetention6DCost            float64 `p:"attributionRetention6DCost"  dc:"6日留存成本"`
	AttributionRetention6DRate            float64 `p:"attributionRetention6DRate"  dc:"6日留存率"`
	AttributionRetention7DCnt             int64   `p:"attributionRetention7DCnt"  dc:"7日留存数"`
	AttributionRetention7DCost            float64 `p:"attributionRetention7DCost"  dc:"7日留存成本"`
	AttributionRetention7DRate            float64 `p:"attributionRetention7DRate"  dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64   `p:"attributionRetention7DSumCnt"  dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64 `p:"attributionRetention7DTotalCost"  dc:"7日留存总成本"`
	TotalPlay                             int64   `p:"totalPlay"  dc:"播放量"`
	ValidPlay                             int64   `p:"validPlay"  dc:"有效播放数"`
	ValidPlayCost                         float64 `p:"validPlayCost"  dc:"有效播放成本"`
	ValidPlayRate                         float64 `p:"validPlayRate"  dc:"有效播放率"`
	Play25FeedBreak                       int64   `p:"play25FeedBreak"  dc:"25%进度播放数"`
	Play50FeedBreak                       int64   `p:"play50FeedBreak"  dc:"50%进度播放数"`
	Play75FeedBreak                       int64   `p:"play75FeedBreak"  dc:"75%进度播放数"`
	Play99FeedBreak                       int64   `p:"play99FeedBreak"  dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64 `p:"averagePlayTimePerPlay"  dc:"平均单次播放时长"`
	PlayOverRate                          float64 `p:"playOverRate"  dc:"完播率"`
	WifiPlayRate                          float64 `p:"wifiPlayRate"  dc:"WiFi播放占比"`
	CardShow                              int64   `p:"cardShow"  dc:"3秒卡片展现数"`
	DyLike                                int64   `p:"dyLike"  dc:"点赞数"`
	DyComment                             int64   `p:"dyComment"  dc:"评论量"`
	DyShare                               int64   `p:"dyShare"  dc:"分享量"`
	IesChallengeClick                     int64   `p:"iesChallengeClick"  dc:"挑战赛查看数"`
	IesMusicClick                         int64   `p:"iesMusicClick"  dc:"音乐查看数"`
	LocationClick                         int64   `p:"locationClick"  dc:"POI点击数"`
	CustomerEffective                     int64   `p:"customerEffective"  dc:"有效获客"`
	Wechat                                int64   `p:"wechat"  dc:"微信复制"`
	AttributionMicroGame0DLtv             float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64 `p:"attributionMicroGame3DLtv"  dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64 `p:"attributionMicroGame7DLtv"  dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64 `p:"attributionMicroGame0DRoi"  dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64 `p:"attributionMicroGame3DRoi"  dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64 `p:"attributionMicroGame7DRoi"  dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreateDate                            string  `p:"createDate"  dc:"统计日期"`
	TotalAmount                           float64 `p:"totalAmount"  dc:"总充值金额"`
	TotalAdUp                             float64 `p:"totalAdUp"  dc:"广告收入"`
	Register                              int     `p:"register"  dc:"注册数"`
}

type AdPromotionReportDataSearch struct {
	comModel.PageReq
	PromotionIds   []string `p:"promotionIds"  dc:"广告id"`
	PromotionNames []string `p:"promotionNames"  dc:"广告名称"`
	DeptIds        []int    `p:"deptIds"  dc:"部门ID"`
	UserIds        []int    `p:"userIds"  dc:"优化师"`
	Companies      []string `p:"companies"  dc:"账户主体"`
	ProjectIds     []string `p:"projectIds"  dc:"项目ids"`
	DeliveryMode   string   `p:"deliveryMode"  dc:"投放方式" dc:"投放模式，MANUAL手动投放、PROCEDURAL自动投放"`
	AdType         string   `p:"adType"  dc:"广告类型" dc:"广告类型，枚举值：ALL所有广告，SEARCH 搜索广告"`
	StartTime      string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime        string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	Status         string   `p:"status" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"`
	Fields         []string `p:"fields" dc:"需要筛选的列名"`
	AdvertiserId   string   `p:"advertiserId"  dc:"广告账户id"`
	ProjectId      string   `p:"projectId"  dc:"项目id"`
	Keyword        string   `p:"keyword"  dc:"关键字"`
}

type AdPromotionReportDataSearchRes struct {
	comModel.ListRes
	List    []*AdPromotionReportDataRes      `json:"list"`
	Summary *AdPromotionReportDataSummaryRes `json:"summary"`
}

type AdPromotionReportDataSearchRes2 struct {
	comModel.ListRes
	List    []*AdPromotionReportDataRes2      `json:"list"`
	Summary *AdPromotionReportDataSummaryRes2 `json:"summary"`
}

type AdPromotionMaterialReportDataSearchRes struct {
	comModel.ListRes
	List    []*AdPromotionMaterialReportDataRes  `json:"list"`
	Summary *AdPromotionMaterialReportSummaryRes `json:"summary"`
}

type AdPromotionMaterialReportDataRes struct {
	CreateDate     string  `json:"createDate" dc:"统计日期"`
	MaterialName   string  `json:"materialName" dc:"素材名称"`
	MaterialDir    string  `json:"materialDir" dc:"素材目录"`
	Designer       string  `json:"designer" dc:"设计师"`
	PromotionCount int     `json:"promotionCount" dc:"关联广告数"`
	StatCost       float64 `json:"statCost" dc:"消耗"`
	Impressions    int64   `json:"impressions" dc:"展示数"`
	CpmPlatform    float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	//AvgCPM         float64 `json:"avgCpm" dc:"平均千次展现成本"`
	Clicks         int64   `json:"clicks" dc:"点击数"`
	Ctr            float64 `json:"ctr" dc:"点击率"`
	Conversions    int64   `json:"conversions" dc:"转化数"`
	Cpa            float64 `json:"cpa" dc:"转化成本"`
	ConversionRate float64 `json:"conversionRate" dc:"转化率"`
	Actives        int64   `json:"actives" dc:"激活数"`
	ActiveCost     float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate     float64 `json:"activeRate" dc:"激活率"`
	FirstPayCount  int64   `json:"firstPayCount" dc:"首次付费数"`
	FirstPayRate   float64 `json:"firstPayRate" dc:"首次付费率"`

	// 素材id
	MaterialID int    `json:"materialId" dc:"素材id"`
	DesignerID string `json:"designerId" dc:"设计师Id"`
}

type AdPromotionMaterialReportSummaryRes struct {
	StatCost    float64 `json:"statCost" dc:"消耗"`
	Impressions int64   `json:"impressions" dc:"展示数"`
	CpmPlatform float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	//AvgCPM         float64 `json:"avgCpm" dc:"平均千次展现成本"`
	Clicks         int64   `json:"clicks" dc:"点击数"`
	Ctr            float64 `json:"ctr" dc:"点击率"`
	Conversions    int64   `json:"conversions" dc:"转化数"`
	Cpa            float64 `json:"cpa" dc:"转化成本"`
	ConversionRate float64 `json:"conversionRate" dc:"转化率"`
	Actives        int64   `json:"actives" dc:"激活数"`
	ActiveCost     float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate     float64 `json:"activeRate" dc:"激活率"`
	FirstPayCount  int64   `json:"firstPayCount" dc:"首次付费数"`
	FirstPayRate   float64 `json:"firstPayRate" dc:"首次付费率"`
}

type AdPromotionAccountReportDataSearchRes struct {
	comModel.ListRes
	List    []*AdPromotionAccountReportDataRes      `json:"list"`
	Summary *AdPromotionAccountReportDataSummaryRes `json:"summary"`
}

type AdPromotionAccountReportDataRes struct {
	CreateDate    string `json:"createDate" dc:"日期"`
	PromotionName string `json:"promotionName" dc:"广告名称"`
	PromotionId   string `json:"promotionId"  dc:"广告ID"`
	// 推广目的
	//LandingType string `json:"landingType" dc:"推广目的"`
	// 账户名称
	AdvertiserName    string  `json:"advertiserName"  dc:"账户名称"`
	AdvertiserCompany string  `json:"advertiserCompany"  dc:"账户主体"`
	UserName          string  `json:"userName"  dc:"优化师"`
	TotalAdNums       int     `json:"totalAdNums" dc:"搭建广告数"`
	StatCost          float64 `json:"statCost" dc:"消耗"`
	StatPayAmount     float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi      float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	ShowCnt           int64   `json:"showCnt" dc:"展示数"`
	// 平均千展费用
	CpmPlatform                 float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                    int64   `json:"clickCnt" dc:"点击数"`
	Ctr                         float64 `json:"ctr" dc:"点击率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost              float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveCost                  float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	ActivePay                   int64   `json:"activePay" dc:"首次付费数"`
	ActivePayRate               float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                int64   `json:"gamePayCount" dc:"付费次数"`
	// 付费率 付费次数/激活数*100
	PayRate float64 `json:"payRate" dc:"付费率 "`

	//Id            int64   `json:"id"`
	//ActivePayCost float64 `json:"activePayCost" dc:"首次付费成本"`
	//ProjectId     string  `json:"projectId"  dc:"项目ID"`
	AdvertiserId string `json:"advertiserId"  dc:"广告账户id"`
	//Status              string  `json:"status" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"`
	//OptStatus           string  `json:"optStatus" dc:"操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）"`
	//Budget              float64 `json:"budget"  dc:"广告预算"`
	//BudgetMode          string  `json:"budgetMode"  dc:"预算类型"`
	//PromotionCreateTime string  `json:"promotionCreateTime"  dc:"广告创建时间"`
	//CpaBid              float64 `json:"cpaBid"  dc:"广告出价：目标转化出价/预期成本"`
	//DeepCpaBid          float64 `json:"deepCpaBid"  dc:"深度优化出价"`
	//UserId              int     `json:"userId"  dc:"优化师ID"`

	//CpcPlatform float64 `json:"cpcPlatform" dc:"平均点击单价(元)"`
	//DeepConvertCnt             int64   `json:"deepConvertCnt" dc:"深度转化数"`
	//DeepConvertCost            float64 `json:"deepConvertCost" dc:"深度转化成本"`
	//DeepConvertRate            float64 `json:"deepConvertRate" dc:"深度转化率"`
	//AttributionConvertCnt      int64   `json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	//AttributionConvertCost     float64 `json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	//AttributionDeepConvertCnt  int64   `json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	//AttributionDeepConvertCost float64 `json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	//PreConvertCount            int64   `json:"preConvertCount" dc:"预估转化数(计费时间)"`
	//PreConvertCost             float64 `json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	//PreConvertRate             float64 `json:"preConvertRate" dc:"预估转化率(计费时间)"`
	//ClickStartCnt              int64   `json:"clickStartCnt" dc:"安卓下载开始数"`
	//ClickStartCost             float64 `json:"clickStartCost" dc:"安卓下载开始成本"`
	//ClickStartRate             float64 `json:"clickStartRate" dc:"安卓下载开始率"`
	//DownloadFinishCnt          int64   `json:"downloadFinishCnt" dc:"安卓下载完成数"`
	//DownloadFinishCost         float64 `json:"downloadFinishCost" dc:"安卓下载完成成本"`
	//DownloadFinishRate         float64 `json:"downloadFinishRate" dc:"安卓下载完成率"`
	//InstallFinishCnt           int64   `json:"installFinishCnt" dc:"安卓安装完成数"`
	//InstallFinishCost          float64 `json:"installFinishCost" dc:"安卓安装完成成本"`
	//InstallFinishRate          float64 `json:"installFinishRate" dc:"安卓安装完成率"`
	//ActiveRegisterCost                    float64 `json:"activeRegisterCost" dc:"注册成本"`
	//ActiveRegisterRate                    float64 `json:"activeRegisterRate" dc:"注册率"`
	//GameAddiction                         int64   `json:"gameAddiction" dc:"关键行为数"`
	//GameAddictionCost                     float64 `json:"gameAddictionCost" dc:"关键行为成本"`
	//GameAddictionRate                     float64 `json:"gameAddictionRate" dc:"关键行为率"`
	//AttributionNextDayOpenCnt             int64   `json:"attributionNextDayOpenCnt" dc:"次留数"`
	//AttributionNextDayOpenCost            float64 `json:"attributionNextDayOpenCost" dc:"次留成本"`
	//AttributionNextDayOpenRate            float64 `json:"attributionNextDayOpenRate" dc:"次留率"`
	//NextDayOpen                           int64   `json:"nextDayOpen" dc:"次留回传数"`
	//GamePayCost                           float64 `json:"gamePayCost" dc:"付费成本"`
	//AttributionGamePay7DCount             int64   `json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`
	//AttributionGamePay7DCost              float64 `json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`
	//AttributionActivePay7DPerCount        int64   `json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`
	//InAppUv                               int64   `json:"inAppUv" dc:"APP内访问"`
	//InAppDetailUv                         int64   `json:"inAppDetailUv" dc:"APP内访问详情页"`
	//InAppCart                             int64   `json:"inAppCart" dc:"APP内加入购物车"`
	//InAppPay                              int64   `json:"inAppPay" dc:"APP内付费"`
	//InAppOrder                            int64   `json:"inAppOrder" dc:"APP内下单"`
	//AttributionGameInAppLtv2Days          float64 `json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`
	//AttributionGameInAppLtv3Days          float64 `json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`
	//AttributionGameInAppLtv4Days          float64 `json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`
	//AttributionGameInAppLtv5Days          float64 `json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`
	//AttributionGameInAppLtv6Days          float64 `json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`
	//AttributionGameInAppLtv7Days          float64 `json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`
	//AttributionGameInAppLtv8Days          float64 `json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`
	//AttributionGameInAppRoi2Days          float64 `json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`
	//AttributionGameInAppRoi3Days          float64 `json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`
	//AttributionGameInAppRoi4Days          float64 `json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`
	//AttributionGameInAppRoi5Days          float64 `json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`
	//AttributionGameInAppRoi6Days          float64 `json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`
	//AttributionGameInAppRoi7Days          float64 `json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`
	//AttributionGameInAppRoi8Days          float64 `json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`
	//AttributionDayActivePayCount          int64   `json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	//AttributionActivePayIntraOneDayCount  int64   `json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	//AttributionActivePayIntraOneDayCost   float64 `json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	//AttributionActivePayIntraOneDayRate   float64 `json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	//AttributionActivePayIntraOneDayAmount float64 `json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	//AttributionActivePayIntraOneDayRoi    float64 `json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	//AttributionRetention2DCnt             int64   `json:"attributionRetention2DCnt" dc:"2日留存数"`
	//AttributionRetention2DCost            float64 `json:"attributionRetention2DCost" dc:"2日留存成本"`
	//AttributionRetention2DRate            float64 `json:"attributionRetention2DRate" dc:"2日留存率"`
	//AttributionRetention3DCnt             int64   `json:"attributionRetention3DCnt" dc:"3日留存数"`
	//AttributionRetention3DCost            float64 `json:"attributionRetention3DCost" dc:"3日留存成本"`
	//AttributionRetention3DRate            float64 `json:"attributionRetention3DRate" dc:"3日留存率"`
	//AttributionRetention4DCnt             int64   `json:"attributionRetention4DCnt" dc:"4日留存数"`
	//AttributionRetention4DCost            float64 `json:"attributionRetention4DCost" dc:"4日留存成本"`
	//AttributionRetention4DRate            float64 `json:"attributionRetention4DRate" dc:"4日留存率"`
	//AttributionRetention5DCnt             int64   `json:"attributionRetention5DCnt" dc:"5日留存数"`
	//AttributionRetention5DCost            float64 `json:"attributionRetention5DCost" dc:"5日留存成本"`
	//AttributionRetention5DRate            float64 `json:"attributionRetention5DRate" dc:"5日留存率"`
	//AttributionRetention6DCnt             int64   `json:"attributionRetention6DCnt" dc:"6日留存数"`
	//AttributionRetention6DCost            float64 `json:"attributionRetention6DCost" dc:"6日留存成本"`
	//AttributionRetention6DRate            float64 `json:"attributionRetention6DRate" dc:"6日留存率"`
	//AttributionRetention7DCnt             int64   `json:"attributionRetention7DCnt" dc:"7日留存数"`
	//AttributionRetention7DCost            float64 `json:"attributionRetention7DCost" dc:"7日留存成本"`
	//AttributionRetention7DRate            float64 `json:"attributionRetention7DRate" dc:"7日留存率"`
	//AttributionRetention7DSumCnt          int64   `json:"attributionRetention7DSumCnt" dc:"7日留存总数"`
	//AttributionRetention7DTotalCost       float64 `json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`
	//TotalPlay                             int64   `json:"totalPlay" dc:"播放量"`
	//ValidPlay                             int64   `json:"validPlay" dc:"有效播放数"`
	//ValidPlayCost                         float64 `json:"validPlayCost" dc:"有效播放成本"`
	//ValidPlayRate                         float64 `json:"validPlayRate" dc:"有效播放率"`
	//Play25FeedBreak                       int64   `json:"play25FeedBreak" dc:"25%进度播放数"`
	//Play50FeedBreak                       int64   `json:"play50FeedBreak" dc:"50%进度播放数"`
	//Play75FeedBreak                       int64   `json:"play75FeedBreak" dc:"75%进度播放数"`
	//Play99FeedBreak                       int64   `json:"play99FeedBreak" dc:"99%进度播放数"`
	//AveragePlayTimePerPlay                float64 `json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	//PlayOverRate                          float64 `json:"playOverRate" dc:"完播率"`
	//WifiPlayRate                          float64 `json:"wifiPlayRate" dc:"WiFi播放占比"`
	//CardShow                              int64   `json:"cardShow" dc:"3秒卡片展现数"`
	//DyLike                                int64   `json:"dyLike" dc:"点赞数"`
	//DyComment                             int64   `json:"dyComment" dc:"评论量"`
	//DyShare                               int64   `json:"dyShare" dc:"分享量"`
	//IesChallengeClick                     int64   `json:"iesChallengeClick" dc:"挑战赛查看数"`
	//IesMusicClick                         int64   `json:"iesMusicClick" dc:"音乐查看数"`
	//LocationClick                         int64   `json:"locationClick" dc:"POI点击数"`
	//CustomerEffective                     int64   `json:"customerEffective" dc:"有效获客"`
	//Wechat                                int64   `json:"wechat" dc:"微信复制"`
	//AttributionMicroGame3DLtv             float64 `json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`
	//AttributionMicroGame7DLtv             float64 `json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`
	//AttributionMicroGame3DRoi             float64 `json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	//AttributionMicroGame7DRoi             float64 `json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	//TotalAmount                           float64 `json:"totalAmount" dc:"总充值金额"`
	//TotalAdUp                             float64 `json:"totalAdUp" dc:"广告收入"`
	//Register                              int     `json:"register" dc:"注册数"`
}

type AdPromotionAccountReportDataSummaryRes struct {
	StatCost      float64 `json:"statCost" dc:"消耗"`
	StatPayAmount float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi  float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	ShowCnt       int64   `json:"showCnt" dc:"展示数"`
	// 平均千展费用
	CpmPlatform                 float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                    int64   `json:"clickCnt" dc:"点击数"`
	Ctr                         float64 `json:"ctr" dc:"点击率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost              float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveCost                  float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	ActivePay                   int64   `json:"activePay" dc:"首次付费数"`
	ActivePayRate               float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                int64   `json:"gamePayCount" dc:"付费次数"`
	// 付费率 付费次数/激活数*100
	PayRate float64 `json:"payRate" dc:"付费率 "`
}

type AdPromotionReportDataSearch2 struct {
	comModel.PageReq
	PromotionIds   []string `p:"promotionIds"  dc:"广告id"`
	PromotionNames []string `p:"promotionNames"  dc:"广告名称"`
	DeptIds        []int    `p:"deptIds"  dc:"部门ID"`
	UserIds        []int    `p:"userIds"  dc:"优化师"`
	Companies      []string `p:"companies"  dc:"账户主体"`
	ProjectIds     []string `p:"projectIds"  dc:"项目ids"`
	LandingType    string   `p:"landingType"  dc:"推广类型" dc:"推广类型，枚举值：APP 应用推广、WECHAT 微信推广、WECHAT_FEED 微信朋友圈推广、WECHAT_SHOP 微信公众号推广、WECHAT_MP 微信小程序推广、WECHAT_GAME 微信小游戏推广、WECHAT_OFFICIAL_ACCOUNT 微信公众号推广、WECHAT_FEED_GAME 微信朋友圈推广、WECHAT_SHOP_GAME 微信公众号推广、WECHAT_MP_GAME 微信小程序推广、WECHAT_G:"`
	DeliveryMode   string   `p:"deliveryMode"  dc:"投放方式" dc:"投放模式，MANUAL手动投放、PROCEDURAL自动投放"`
	AdType         string   `p:"adType"  dc:"广告类型" dc:"广告类型，枚举值：ALL所有广告，SEARCH 搜索广告"`
	StartTime      string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime        string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	Fields         []string `p:"fields" dc:"需要筛选的列名"`
	AdvertiserId   string   `p:"advertiserId"  dc:"广告账户id"`
	ProjectId      string   `p:"projectId"  dc:"项目id"`
	Keyword        string   `p:"keyword"  dc:"关键字"`
}

type AdPromotionAccountReportDataSearch struct {
	comModel.PageReq
	PromotionIds   []string `p:"promotionIds"  dc:"广告id"`
	PromotionNames []string `p:"promotionNames"  dc:"广告名称"`
	DeptIds        []int    `p:"deptIds"  dc:"部门ID"`
	UserIds        []int    `p:"userIds"  dc:"优化师"`
	Companies      []string `p:"companies"  dc:"账户主体"`
	AdvertiserId   string   `p:"advertiserId"  dc:"广告账户id"`
	AccountName    string   `p:"accountName"  dc:"账户名称"`
	ProjectIds     []string `p:"projectIds"  dc:"项目ids"`
	DeliveryMode   string   `p:"deliveryMode"  dc:"投放方式" dc:"投放模式，MANUAL手动投放、PROCEDURAL自动投放"`
	AdType         string   `p:"adType"  dc:"广告类型" dc:"广告类型，枚举值：ALL所有广告，SEARCH 搜索广告"`
	StartTime      string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime        string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	Fields         []string `p:"fields" dc:"需要筛选的列名"`
	ProjectId      string   `p:"projectId"  dc:"项目id"`
	Keyword        string   `p:"keyword"  dc:"关键字"`
}

type AdPromotionMaterialStatisticsReq struct {
	comModel.PageReq
	StartTime string `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime   string `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	DeptIds   []int  `p:"deptIds"  dc:"部门ID"`
	//素材目录
	FileId int `p:"fileId"  dc:"素材目录"`
	// 素材id
	MaterialIds  []string `p:"materialIds"  dc:"素材id"`
	UserIds      []int    `p:"userIds"  dc:"设计师"`
	AdvertiserId string   `p:"advertiserId"  dc:"广告账户id"`
	Companies    []string `p:"companies"  dc:"账户主体"`
	ProjectIds   []string `p:"projectIds"  dc:"项目ids"`
	Fields       []string `p:"fields" dc:"需要筛选的列名"`
	ProjectId    string   `p:"projectId"  dc:"项目id"`
	Keyword      string   `p:"keyword"  dc:"关键字"`
}

type AdPromotionReportDataSummaryRes2 struct {
	StatCost      float64 `json:"statCost" dc:"消耗"`
	StatPayAmount float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi  float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	ShowCnt       int64   `json:"showCnt" dc:"展示数"`
	// 平均千展费用
	CpmPlatform                 float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                    int64   `json:"clickCnt" dc:"点击数"`
	Ctr                         float64 `json:"ctr" dc:"点击率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost              float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveCost                  float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	ActivePay                   int64   `json:"activePay" dc:"首次付费数"`
	ActivePayRate               float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                int64   `json:"gamePayCount" dc:"付费次数"`
	// 付费率 付费次数/激活数*100
	PayRate float64 `json:"payRate" dc:"付费率 "`
}
type AdPromotionReportDataRes2 struct {
	CreateDate    string `json:"createDate" dc:"统计日期"`
	PromotionName string `json:"promotionName" dc:"广告名称"`
	PromotionId   string `json:"promotionId"  dc:"广告ID"`
	// 推广目的
	LandingType string `json:"landingType" dc:"推广目的"`
	// 账户名称
	AdvertiserName    string  `json:"advertiserName"  dc:"账户名称"`
	AdvertiserCompany string  `json:"advertiserCompany"  dc:"账户主体"`
	UserName          string  `json:"userName"  dc:"优化师"`
	StatCost          float64 `json:"statCost" dc:"消耗"`
	StatPayAmount     float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi      float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	ShowCnt           int64   `json:"showCnt" dc:"展示数"`
	// 平均千展费用
	CpmPlatform                 float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                    int64   `json:"clickCnt" dc:"点击数"`
	Ctr                         float64 `json:"ctr" dc:"点击率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost              float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveCost                  float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	ActivePay                   int64   `json:"activePay" dc:"首次付费数"`
	ActivePayRate               float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                int64   `json:"gamePayCount" dc:"付费次数"`
	// 付费率 付费次数/激活数*100
	PayRate float64 `json:"payRate" dc:"付费率 "`

	//Id            int64   `json:"id"`
	//ActivePayCost float64 `json:"activePayCost" dc:"首次付费成本"`
	//ProjectId     string  `json:"projectId"  dc:"项目ID"`
	//AdvertiserId  string  `json:"advertiserId"  dc:"广告账户id"`
	//Status              string  `json:"status" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"`
	//OptStatus           string  `json:"optStatus" dc:"操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）"`
	//Budget              float64 `json:"budget"  dc:"广告预算"`
	//BudgetMode          string  `json:"budgetMode"  dc:"预算类型"`
	//PromotionCreateTime string  `json:"promotionCreateTime"  dc:"广告创建时间"`
	//CpaBid              float64 `json:"cpaBid"  dc:"广告出价：目标转化出价/预期成本"`
	//DeepCpaBid          float64 `json:"deepCpaBid"  dc:"深度优化出价"`
	//UserId              int     `json:"userId"  dc:"优化师ID"`
	//CpmPlatform float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	//CpcPlatform float64 `json:"cpcPlatform" dc:"平均点击单价(元)"`
	//DeepConvertCnt             int64   `json:"deepConvertCnt" dc:"深度转化数"`
	//DeepConvertCost            float64 `json:"deepConvertCost" dc:"深度转化成本"`
	//DeepConvertRate            float64 `json:"deepConvertRate" dc:"深度转化率"`
	//AttributionConvertCnt      int64   `json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	//AttributionConvertCost     float64 `json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	//AttributionDeepConvertCnt  int64   `json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	//AttributionDeepConvertCost float64 `json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	//PreConvertCount            int64   `json:"preConvertCount" dc:"预估转化数(计费时间)"`
	//PreConvertCost             float64 `json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	//PreConvertRate             float64 `json:"preConvertRate" dc:"预估转化率(计费时间)"`
	//ClickStartCnt              int64   `json:"clickStartCnt" dc:"安卓下载开始数"`
	//ClickStartCost             float64 `json:"clickStartCost" dc:"安卓下载开始成本"`
	//ClickStartRate             float64 `json:"clickStartRate" dc:"安卓下载开始率"`
	//DownloadFinishCnt          int64   `json:"downloadFinishCnt" dc:"安卓下载完成数"`
	//DownloadFinishCost         float64 `json:"downloadFinishCost" dc:"安卓下载完成成本"`
	//DownloadFinishRate         float64 `json:"downloadFinishRate" dc:"安卓下载完成率"`
	//InstallFinishCnt           int64   `json:"installFinishCnt" dc:"安卓安装完成数"`
	//InstallFinishCost          float64 `json:"installFinishCost" dc:"安卓安装完成成本"`
	//InstallFinishRate          float64 `json:"installFinishRate" dc:"安卓安装完成率"`
	//ActiveRegisterCost                    float64 `json:"activeRegisterCost" dc:"注册成本"`
	//ActiveRegisterRate                    float64 `json:"activeRegisterRate" dc:"注册率"`
	//GameAddiction                         int64   `json:"gameAddiction" dc:"关键行为数"`
	//GameAddictionCost                     float64 `json:"gameAddictionCost" dc:"关键行为成本"`
	//GameAddictionRate                     float64 `json:"gameAddictionRate" dc:"关键行为率"`
	//AttributionNextDayOpenCnt             int64   `json:"attributionNextDayOpenCnt" dc:"次留数"`
	//AttributionNextDayOpenCost            float64 `json:"attributionNextDayOpenCost" dc:"次留成本"`
	//AttributionNextDayOpenRate            float64 `json:"attributionNextDayOpenRate" dc:"次留率"`
	//NextDayOpen                           int64   `json:"nextDayOpen" dc:"次留回传数"`
	//GamePayCost                           float64 `json:"gamePayCost" dc:"付费成本"`
	//AttributionGamePay7DCount             int64   `json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`
	//AttributionGamePay7DCost              float64 `json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`
	//AttributionActivePay7DPerCount        int64   `json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`
	//InAppUv                               int64   `json:"inAppUv" dc:"APP内访问"`
	//InAppDetailUv                         int64   `json:"inAppDetailUv" dc:"APP内访问详情页"`
	//InAppCart                             int64   `json:"inAppCart" dc:"APP内加入购物车"`
	//InAppPay                              int64   `json:"inAppPay" dc:"APP内付费"`
	//InAppOrder                            int64   `json:"inAppOrder" dc:"APP内下单"`
	//AttributionGameInAppLtv2Days          float64 `json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`
	//AttributionGameInAppLtv3Days          float64 `json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`
	//AttributionGameInAppLtv4Days          float64 `json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`
	//AttributionGameInAppLtv5Days          float64 `json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`
	//AttributionGameInAppLtv6Days          float64 `json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`
	//AttributionGameInAppLtv7Days          float64 `json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`
	//AttributionGameInAppLtv8Days          float64 `json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`
	//AttributionGameInAppRoi2Days          float64 `json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`
	//AttributionGameInAppRoi3Days          float64 `json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`
	//AttributionGameInAppRoi4Days          float64 `json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`
	//AttributionGameInAppRoi5Days          float64 `json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`
	//AttributionGameInAppRoi6Days          float64 `json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`
	//AttributionGameInAppRoi7Days          float64 `json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`
	//AttributionGameInAppRoi8Days          float64 `json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`
	//AttributionDayActivePayCount          int64   `json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	//AttributionActivePayIntraOneDayCount  int64   `json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	//AttributionActivePayIntraOneDayCost   float64 `json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	//AttributionActivePayIntraOneDayRate   float64 `json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	//AttributionActivePayIntraOneDayAmount float64 `json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	//AttributionActivePayIntraOneDayRoi    float64 `json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	//AttributionRetention2DCnt             int64   `json:"attributionRetention2DCnt" dc:"2日留存数"`
	//AttributionRetention2DCost            float64 `json:"attributionRetention2DCost" dc:"2日留存成本"`
	//AttributionRetention2DRate            float64 `json:"attributionRetention2DRate" dc:"2日留存率"`
	//AttributionRetention3DCnt             int64   `json:"attributionRetention3DCnt" dc:"3日留存数"`
	//AttributionRetention3DCost            float64 `json:"attributionRetention3DCost" dc:"3日留存成本"`
	//AttributionRetention3DRate            float64 `json:"attributionRetention3DRate" dc:"3日留存率"`
	//AttributionRetention4DCnt             int64   `json:"attributionRetention4DCnt" dc:"4日留存数"`
	//AttributionRetention4DCost            float64 `json:"attributionRetention4DCost" dc:"4日留存成本"`
	//AttributionRetention4DRate            float64 `json:"attributionRetention4DRate" dc:"4日留存率"`
	//AttributionRetention5DCnt             int64   `json:"attributionRetention5DCnt" dc:"5日留存数"`
	//AttributionRetention5DCost            float64 `json:"attributionRetention5DCost" dc:"5日留存成本"`
	//AttributionRetention5DRate            float64 `json:"attributionRetention5DRate" dc:"5日留存率"`
	//AttributionRetention6DCnt             int64   `json:"attributionRetention6DCnt" dc:"6日留存数"`
	//AttributionRetention6DCost            float64 `json:"attributionRetention6DCost" dc:"6日留存成本"`
	//AttributionRetention6DRate            float64 `json:"attributionRetention6DRate" dc:"6日留存率"`
	//AttributionRetention7DCnt             int64   `json:"attributionRetention7DCnt" dc:"7日留存数"`
	//AttributionRetention7DCost            float64 `json:"attributionRetention7DCost" dc:"7日留存成本"`
	//AttributionRetention7DRate            float64 `json:"attributionRetention7DRate" dc:"7日留存率"`
	//AttributionRetention7DSumCnt          int64   `json:"attributionRetention7DSumCnt" dc:"7日留存总数"`
	//AttributionRetention7DTotalCost       float64 `json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`
	//TotalPlay                             int64   `json:"totalPlay" dc:"播放量"`
	//ValidPlay                             int64   `json:"validPlay" dc:"有效播放数"`
	//ValidPlayCost                         float64 `json:"validPlayCost" dc:"有效播放成本"`
	//ValidPlayRate                         float64 `json:"validPlayRate" dc:"有效播放率"`
	//Play25FeedBreak                       int64   `json:"play25FeedBreak" dc:"25%进度播放数"`
	//Play50FeedBreak                       int64   `json:"play50FeedBreak" dc:"50%进度播放数"`
	//Play75FeedBreak                       int64   `json:"play75FeedBreak" dc:"75%进度播放数"`
	//Play99FeedBreak                       int64   `json:"play99FeedBreak" dc:"99%进度播放数"`
	//AveragePlayTimePerPlay                float64 `json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	//PlayOverRate                          float64 `json:"playOverRate" dc:"完播率"`
	//WifiPlayRate                          float64 `json:"wifiPlayRate" dc:"WiFi播放占比"`
	//CardShow                              int64   `json:"cardShow" dc:"3秒卡片展现数"`
	//DyLike                                int64   `json:"dyLike" dc:"点赞数"`
	//DyComment                             int64   `json:"dyComment" dc:"评论量"`
	//DyShare                               int64   `json:"dyShare" dc:"分享量"`
	//IesChallengeClick                     int64   `json:"iesChallengeClick" dc:"挑战赛查看数"`
	//IesMusicClick                         int64   `json:"iesMusicClick" dc:"音乐查看数"`
	//LocationClick                         int64   `json:"locationClick" dc:"POI点击数"`
	//CustomerEffective                     int64   `json:"customerEffective" dc:"有效获客"`
	//Wechat                                int64   `json:"wechat" dc:"微信复制"`
	//AttributionMicroGame3DLtv             float64 `json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`
	//AttributionMicroGame7DLtv             float64 `json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`
	//AttributionMicroGame3DRoi             float64 `json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	//AttributionMicroGame7DRoi             float64 `json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	//TotalAmount                           float64 `json:"totalAmount" dc:"总充值金额"`
	//TotalAdUp                             float64 `json:"totalAdUp" dc:"广告收入"`
	//Register                              int     `json:"register" dc:"注册数"`
}

type AdPromotionReportDataRes struct {
	Id                                    int64   `json:"id"`
	PromotionId                           string  `json:"promotionId"  dc:"广告ID"`
	PromotionName                         string  `json:"promotionName" dc:"广告名称"`
	ProjectId                             string  `json:"projectId"  dc:"项目ID"`
	AdvertiserId                          string  `json:"advertiserId"  dc:"广告账户id"`
	StatPayAmount                         float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                          float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	Status                                string  `json:"status" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"`
	OptStatus                             string  `json:"optStatus" dc:"操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）"`
	Budget                                float64 `json:"budget"  dc:"广告预算"`
	BudgetMode                            string  `json:"budgetMode"  dc:"预算类型"`
	AdvertiserCompany                     string  `json:"advertiserCompany"  dc:"账户主体"`
	PromotionCreateTime                   string  `json:"promotionCreateTime"  dc:"广告创建时间"`
	CpaBid                                float64 `json:"cpaBid"  dc:"广告出价：目标转化出价/预期成本"`
	DeepCpaBid                            float64 `json:"deepCpaBid"  dc:"深度优化出价"`
	UserId                                int     `json:"userId"  dc:"优化师ID"`
	UserName                              string  `json:"userName"  dc:"优化师"`
	StatCost                              float64 `json:"statCost" dc:"消耗"`
	ShowCnt                               int64   `json:"showCnt" dc:"展示数"`
	CpmPlatform                           float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                              int64   `json:"clickCnt" dc:"点击数"`
	Ctr                                   float64 `json:"ctr" dc:"点击率"`
	CpcPlatform                           float64 `json:"cpcPlatform" dc:"平均点击单价(元)"`
	ConvertCnt                            int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost                        float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate                        float64 `json:"conversionRate" dc:"转化率"`
	DeepConvertCnt                        int64   `json:"deepConvertCnt" dc:"深度转化数"`
	DeepConvertCost                       float64 `json:"deepConvertCost" dc:"深度转化成本"`
	DeepConvertRate                       float64 `json:"deepConvertRate" dc:"深度转化率"`
	AttributionConvertCnt                 int64   `json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	AttributionConvertCost                float64 `json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64   `json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64 `json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64   `json:"preConvertCount" dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64 `json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64 `json:"preConvertRate" dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64   `json:"clickStartCnt" dc:"安卓下载开始数"`
	ClickStartCost                        float64 `json:"clickStartCost" dc:"安卓下载开始成本"`
	ClickStartRate                        float64 `json:"clickStartRate" dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64   `json:"downloadFinishCnt" dc:"安卓下载完成数"`
	DownloadFinishCost                    float64 `json:"downloadFinishCost" dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64 `json:"downloadFinishRate" dc:"安卓下载完成率"`
	InstallFinishCnt                      int64   `json:"installFinishCnt" dc:"安卓安装完成数"`
	InstallFinishCost                     float64 `json:"installFinishCost" dc:"安卓安装完成成本"`
	InstallFinishRate                     float64 `json:"installFinishRate" dc:"安卓安装完成率"`
	Active                                int64   `json:"active" dc:"激活数"`
	ActiveCost                            float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                            float64 `json:"activeRate" dc:"激活率"`
	ActiveRegisterCost                    float64 `json:"activeRegisterCost" dc:"注册成本"`
	ActiveRegisterRate                    float64 `json:"activeRegisterRate" dc:"注册率"`
	GameAddiction                         int64   `json:"gameAddiction" dc:"关键行为数"`
	GameAddictionCost                     float64 `json:"gameAddictionCost" dc:"关键行为成本"`
	GameAddictionRate                     float64 `json:"gameAddictionRate" dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64   `json:"attributionNextDayOpenCnt" dc:"次留数"`
	AttributionNextDayOpenCost            float64 `json:"attributionNextDayOpenCost" dc:"次留成本"`
	AttributionNextDayOpenRate            float64 `json:"attributionNextDayOpenRate" dc:"次留率"`
	NextDayOpen                           int64   `json:"nextDayOpen" dc:"次留回传数"`
	ActivePay                             int64   `json:"activePay" dc:"首次付费数"`
	ActivePayCost                         float64 `json:"activePayCost" dc:"首次付费成本"`
	ActivePayRate                         float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                          int64   `json:"gamePayCount" dc:"付费次数"`
	GamePayCost                           float64 `json:"gamePayCost" dc:"付费成本"`
	AttributionGamePay7DCount             int64   `json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64 `json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64   `json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64   `json:"inAppUv" dc:"APP内访问"`
	InAppDetailUv                         int64   `json:"inAppDetailUv" dc:"APP内访问详情页"`
	InAppCart                             int64   `json:"inAppCart" dc:"APP内加入购物车"`
	InAppPay                              int64   `json:"inAppPay" dc:"APP内付费"`
	InAppOrder                            int64   `json:"inAppOrder" dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64 `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64 `json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64 `json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64 `json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64 `json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64 `json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64 `json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64 `json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64 `json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64 `json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64 `json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64 `json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64 `json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64 `json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64 `json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64   `json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64   `json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64 `json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64 `json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64 `json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64 `json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64   `json:"attributionRetention2DCnt" dc:"2日留存数"`
	AttributionRetention2DCost            float64 `json:"attributionRetention2DCost" dc:"2日留存成本"`
	AttributionRetention2DRate            float64 `json:"attributionRetention2DRate" dc:"2日留存率"`
	AttributionRetention3DCnt             int64   `json:"attributionRetention3DCnt" dc:"3日留存数"`
	AttributionRetention3DCost            float64 `json:"attributionRetention3DCost" dc:"3日留存成本"`
	AttributionRetention3DRate            float64 `json:"attributionRetention3DRate" dc:"3日留存率"`
	AttributionRetention4DCnt             int64   `json:"attributionRetention4DCnt" dc:"4日留存数"`
	AttributionRetention4DCost            float64 `json:"attributionRetention4DCost" dc:"4日留存成本"`
	AttributionRetention4DRate            float64 `json:"attributionRetention4DRate" dc:"4日留存率"`
	AttributionRetention5DCnt             int64   `json:"attributionRetention5DCnt" dc:"5日留存数"`
	AttributionRetention5DCost            float64 `json:"attributionRetention5DCost" dc:"5日留存成本"`
	AttributionRetention5DRate            float64 `json:"attributionRetention5DRate" dc:"5日留存率"`
	AttributionRetention6DCnt             int64   `json:"attributionRetention6DCnt" dc:"6日留存数"`
	AttributionRetention6DCost            float64 `json:"attributionRetention6DCost" dc:"6日留存成本"`
	AttributionRetention6DRate            float64 `json:"attributionRetention6DRate" dc:"6日留存率"`
	AttributionRetention7DCnt             int64   `json:"attributionRetention7DCnt" dc:"7日留存数"`
	AttributionRetention7DCost            float64 `json:"attributionRetention7DCost" dc:"7日留存成本"`
	AttributionRetention7DRate            float64 `json:"attributionRetention7DRate" dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64   `json:"attributionRetention7DSumCnt" dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64 `json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`
	TotalPlay                             int64   `json:"totalPlay" dc:"播放量"`
	ValidPlay                             int64   `json:"validPlay" dc:"有效播放数"`
	ValidPlayCost                         float64 `json:"validPlayCost" dc:"有效播放成本"`
	ValidPlayRate                         float64 `json:"validPlayRate" dc:"有效播放率"`
	Play25FeedBreak                       int64   `json:"play25FeedBreak" dc:"25%进度播放数"`
	Play50FeedBreak                       int64   `json:"play50FeedBreak" dc:"50%进度播放数"`
	Play75FeedBreak                       int64   `json:"play75FeedBreak" dc:"75%进度播放数"`
	Play99FeedBreak                       int64   `json:"play99FeedBreak" dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64 `json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	PlayOverRate                          float64 `json:"playOverRate" dc:"完播率"`
	WifiPlayRate                          float64 `json:"wifiPlayRate" dc:"WiFi播放占比"`
	CardShow                              int64   `json:"cardShow" dc:"3秒卡片展现数"`
	DyLike                                int64   `json:"dyLike" dc:"点赞数"`
	DyComment                             int64   `json:"dyComment" dc:"评论量"`
	DyShare                               int64   `json:"dyShare" dc:"分享量"`
	IesChallengeClick                     int64   `json:"iesChallengeClick" dc:"挑战赛查看数"`
	IesMusicClick                         int64   `json:"iesMusicClick" dc:"音乐查看数"`
	LocationClick                         int64   `json:"locationClick" dc:"POI点击数"`
	CustomerEffective                     int64   `json:"customerEffective" dc:"有效获客"`
	Wechat                                int64   `json:"wechat" dc:"微信复制"`
	AttributionMicroGame0DLtv             float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64 `json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64 `json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64 `json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64 `json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	TotalAmount                           float64 `json:"totalAmount" dc:"总充值金额"`
	TotalAdUp                             float64 `json:"totalAdUp" dc:"广告收入"`
	Register                              int     `json:"register" dc:"注册数"`
}

type AdPromotionReportDataSummaryRes struct {
	StatCost                              float64 `json:"statCost" dc:"消耗"`
	ShowCnt                               int64   `json:"showCnt" dc:"展示数"`
	CpmPlatform                           float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                              int64   `json:"clickCnt" dc:"点击数"`
	Ctr                                   float64 `json:"ctr" dc:"点击率"`
	CpcPlatform                           float64 `json:"cpcPlatform" dc:"平均点击单价(元)"`
	ConvertCnt                            int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost                        float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate                        float64 `json:"conversionRate" dc:"转化率"`
	DeepConvertCnt                        int64   `json:"deepConvertCnt" dc:"深度转化数"`
	DeepConvertCost                       float64 `json:"deepConvertCost" dc:"深度转化成本"`
	DeepConvertRate                       float64 `json:"deepConvertRate" dc:"深度转化率"`
	AttributionConvertCnt                 int64   `json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	AttributionConvertCost                float64 `json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64   `json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64 `json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64   `json:"preConvertCount" dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64 `json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64 `json:"preConvertRate" dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64   `json:"clickStartCnt" dc:"安卓下载开始数"`
	ClickStartCost                        float64 `json:"clickStartCost" dc:"安卓下载开始成本"`
	ClickStartRate                        float64 `json:"clickStartRate" dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64   `json:"downloadFinishCnt" dc:"安卓下载完成数"`
	DownloadFinishCost                    float64 `json:"downloadFinishCost" dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64 `json:"downloadFinishRate" dc:"安卓下载完成率"`
	InstallFinishCnt                      int64   `json:"installFinishCnt" dc:"安卓安装完成数"`
	InstallFinishCost                     float64 `json:"installFinishCost" dc:"安卓安装完成成本"`
	InstallFinishRate                     float64 `json:"installFinishRate" dc:"安卓安装完成率"`
	Active                                int64   `json:"active" dc:"激活数"`
	ActiveCost                            float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                            float64 `json:"activeRate" dc:"激活率"`
	ActiveRegisterCost                    float64 `json:"activeRegisterCost" dc:"注册成本"`
	ActiveRegisterRate                    float64 `json:"activeRegisterRate" dc:"注册率"`
	GameAddiction                         int64   `json:"gameAddiction" dc:"关键行为数"`
	GameAddictionCost                     float64 `json:"gameAddictionCost" dc:"关键行为成本"`
	GameAddictionRate                     float64 `json:"gameAddictionRate" dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64   `json:"attributionNextDayOpenCnt" dc:"次留数"`
	AttributionNextDayOpenCost            float64 `json:"attributionNextDayOpenCost" dc:"次留成本"`
	AttributionNextDayOpenRate            float64 `json:"attributionNextDayOpenRate" dc:"次留率"`
	NextDayOpen                           int64   `json:"nextDayOpen" dc:"次留回传数"`
	ActivePay                             int64   `json:"activePay" dc:"首次付费数"`
	ActivePayCost                         float64 `json:"activePayCost" dc:"首次付费成本"`
	ActivePayRate                         float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                          int64   `json:"gamePayCount" dc:"付费次数"`
	GamePayCost                           float64 `json:"gamePayCost" dc:"付费成本"`
	AttributionGamePay7DCount             int64   `json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64 `json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64   `json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64   `json:"inAppUv" dc:"APP内访问"`
	InAppDetailUv                         int64   `json:"inAppDetailUv" dc:"APP内访问详情页"`
	InAppCart                             int64   `json:"inAppCart" dc:"APP内加入购物车"`
	InAppPay                              int64   `json:"inAppPay" dc:"APP内付费"`
	InAppOrder                            int64   `json:"inAppOrder" dc:"APP内下单"`
	StatPayAmount                         float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                          float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	AttributionGameInAppLtv1Day           float64 `json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64 `json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64 `json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64 `json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64 `json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64 `json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64 `json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64 `json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64 `json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64 `json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64 `json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64 `json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64 `json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64 `json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64 `json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64   `json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64   `json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64 `json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64 `json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64 `json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64 `json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64   `json:"attributionRetention2DCnt" dc:"2日留存数"`
	AttributionRetention2DCost            float64 `json:"attributionRetention2DCost" dc:"2日留存成本"`
	AttributionRetention2DRate            float64 `json:"attributionRetention2DRate" dc:"2日留存率"`
	AttributionRetention3DCnt             int64   `json:"attributionRetention3DCnt" dc:"3日留存数"`
	AttributionRetention3DCost            float64 `json:"attributionRetention3DCost" dc:"3日留存成本"`
	AttributionRetention3DRate            float64 `json:"attributionRetention3DRate" dc:"3日留存率"`
	AttributionRetention4DCnt             int64   `json:"attributionRetention4DCnt" dc:"4日留存数"`
	AttributionRetention4DCost            float64 `json:"attributionRetention4DCost" dc:"4日留存成本"`
	AttributionRetention4DRate            float64 `json:"attributionRetention4DRate" dc:"4日留存率"`
	AttributionRetention5DCnt             int64   `json:"attributionRetention5DCnt" dc:"5日留存数"`
	AttributionRetention5DCost            float64 `json:"attributionRetention5DCost" dc:"5日留存成本"`
	AttributionRetention5DRate            float64 `json:"attributionRetention5DRate" dc:"5日留存率"`
	AttributionRetention6DCnt             int64   `json:"attributionRetention6DCnt" dc:"6日留存数"`
	AttributionRetention6DCost            float64 `json:"attributionRetention6DCost" dc:"6日留存成本"`
	AttributionRetention6DRate            float64 `json:"attributionRetention6DRate" dc:"6日留存率"`
	AttributionRetention7DCnt             int64   `json:"attributionRetention7DCnt" dc:"7日留存数"`
	AttributionRetention7DCost            float64 `json:"attributionRetention7DCost" dc:"7日留存成本"`
	AttributionRetention7DRate            float64 `json:"attributionRetention7DRate" dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64   `json:"attributionRetention7DSumCnt" dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64 `json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`
	TotalPlay                             int64   `json:"totalPlay" dc:"播放量"`
	ValidPlay                             int64   `json:"validPlay" dc:"有效播放数"`
	ValidPlayCost                         float64 `json:"validPlayCost" dc:"有效播放成本"`
	ValidPlayRate                         float64 `json:"validPlayRate" dc:"有效播放率"`
	Play25FeedBreak                       int64   `json:"play25FeedBreak" dc:"25%进度播放数"`
	Play50FeedBreak                       int64   `json:"play50FeedBreak" dc:"50%进度播放数"`
	Play75FeedBreak                       int64   `json:"play75FeedBreak" dc:"75%进度播放数"`
	Play99FeedBreak                       int64   `json:"play99FeedBreak" dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64 `json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	PlayOverRate                          float64 `json:"playOverRate" dc:"完播率"`
	WifiPlayRate                          float64 `json:"wifiPlayRate" dc:"WiFi播放占比"`
	CardShow                              int64   `json:"cardShow" dc:"3秒卡片展现数"`
	DyLike                                int64   `json:"dyLike" dc:"点赞数"`
	DyComment                             int64   `json:"dyComment" dc:"评论量"`
	DyShare                               int64   `json:"dyShare" dc:"分享量"`
	IesChallengeClick                     int64   `json:"iesChallengeClick" dc:"挑战赛查看数"`
	IesMusicClick                         int64   `json:"iesMusicClick" dc:"音乐查看数"`
	LocationClick                         int64   `json:"locationClick" dc:"POI点击数"`
	CustomerEffective                     int64   `json:"customerEffective" dc:"有效获客"`
	Wechat                                int64   `json:"wechat" dc:"微信复制"`
	AttributionMicroGame0DLtv             float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64 `json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64 `json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64 `json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64 `json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	TotalAmount                           float64 `json:"totalAmount" dc:"总充值金额"`
	TotalAdUp                             float64 `json:"totalAdUp" dc:"广告收入"`
	Register                              int     `json:"register" dc:"注册数"`
}

type OptimizerDataStatisticsReq struct {
	comModel.PageReq
	StartTime     string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime       string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	UserIds       []uint64 `p:"userIds"  dc:"优化师ID列表"`
	SupervisorIds []uint64 `p:"supervisorIds"  dc:"优化师主管ID列表"`
	DeptIds       []int    `p:"deptIds"  dc:"部门ID列表"`
	AdvertiserIds []string `p:"advertiserIds"  dc:"账户ID列表"`
}

type OptimizerDataStatisticsRes struct {
	comModel.ListRes
	List    []*OptimizerDataStatisticsData `json:"list"`
	Summary *OptimizerDataStatisticsData   `json:"summary"`
}

type OptimizerDataStatisticsData struct {
	CreateDate                  string  `json:"createDate" dc:"统计日期"`
	AdvertiserCompany           string  `json:"advertiserCompany" dc:"账户主体"`
	UserId                      int     `json:"userId" dc:"优化师ID"`
	UserName                    string  `json:"userName" dc:"优化师"`
	SupervisorName              string  `json:"supervisorName" dc:"优化师主管"`
	TotalAdNums                 int     `json:"totalAdNums" dc:"搭建广告数"`
	HasCostAdNums               int     `json:"hasCostAdNums" dc:"有消耗广告数"`
	LearnedAdNums               int     `json:"learnedAdNums" dc:"过学习期广告数"`
	StatCost                    float64 `json:"statCost" dc:"消耗"`
	StatPayAmount               float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	ShowCnt                     int64   `json:"showCnt" dc:"展示数"`
	CpmPlatform                 float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                    int64   `json:"clickCnt" dc:"点击数"`
	Ctr                         float64 `json:"ctr" dc:"点击率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost              float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveCost                  float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"付费金额（激活用户当日付费金额）"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"付费ROI（激活用户当日付费RIO）"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV（激活用户当日LTV）"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI（激活用户当日广告变现ROI）"`
	ActivePay                   int64   `json:"activePay" dc:"首次付费数"`
	ActivePayRate               float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                int64   `json:"gamePayCount" dc:"付费次数"`
}
