// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: api/v1/oceanengine/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAccountSubjectDataStatSearchReq 分页请求参数
type AdAccountSubjectDataStatSearchReq struct {
	g.Meta `path:"/list" tags:"账户主体数据统计" method:"post" summary:"账户主体数据统计列表"`
	commonApi.Author
	model.AdAccountSubjectDataStatSearchReq
}

// AdAccountSubjectDataStatSearchRes 列表返回结果
type AdAccountSubjectDataStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAccountSubjectDataStatSearchRes
}

// AdAccountSubjectDataStatAddReq 添加操作请求参数
type AdAccountSubjectDataStatAddReq struct {
	g.Meta `path:"/add" tags:"账户主体数据统计" method:"post" summary:"账户主体数据统计添加"`
	commonApi.Author
	*model.AdAccountSubjectDataStatAddReq
}

// AdAccountSubjectDataStatAddRes 添加操作返回结果
type AdAccountSubjectDataStatAddRes struct {
	commonApi.EmptyRes
}

// AdAccountSubjectDataStatTaskReq 账户主体数据统计任务请求参数
type AdAccountSubjectDataStatTaskReq struct {
	g.Meta `path:"/task" tags:"账户主体数据统计" method:"post" summary:"账户主体数据统计任务"`
	commonApi.Author
	model.AdAccountSubjectDataStatSearchReq
}

// AdAccountSubjectDataStatTaskRes 账户主体数据统计任务返回结果
type AdAccountSubjectDataStatTaskRes struct {
	commonApi.EmptyRes
}
