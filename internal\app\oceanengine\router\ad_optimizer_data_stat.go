// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/router/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/controller"
)

func (router *Router) BindAdOptimizerDataStatController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adOptimizerDataStat", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdOptimizerDataStat,
		)
	})
}
