// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/dao/internal/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdDesignerMaterialReportDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdDesignerMaterialReportDao struct {
	table   string                          // Table is the underlying table name of the DAO.
	group   string                          // Group is the database configuration group name of current DAO.
	columns AdDesignerMaterialReportColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdDesignerMaterialReportColumns defines and stores column names for table ad_designer_material_report.
type AdDesignerMaterialReportColumns struct {
	StatDate             string // 统计日期
	DesignerName         string // 设计师名称
	DesignerId           string // 设计师名称
	NewMaterialCount     string // 上新素材数
	ExposedMaterialCount string // 曝光大于0的素材数
	NewMaterialCost      string // 上新素材消耗
	TotalCost            string // 总消耗
	Impressions          string // 展示数
	AvgCpm               string // 平均千次展现成本
	Clicks               string // 点击数
	Ctr                  string // 点击率
	Conversions          string // 转化数
	Cpa                  string // 转化成本
	ConversionRate       string // 转化率
	Actives              string // 激活数
	ActiveCost           string // 激活成本
	ActiveRate           string // 激活率
	FirstPayCount        string // 首次付费数
	FirstPayRate         string // 首次付费率
}

var adDesignerMaterialReportColumns = AdDesignerMaterialReportColumns{
	StatDate:             "stat_date",
	DesignerName:         "designer_name",
	DesignerId:           "designer_id",
	NewMaterialCount:     "new_material_count",
	ExposedMaterialCount: "exposed_material_count",
	NewMaterialCost:      "new_material_cost",
	TotalCost:            "total_cost",
	Impressions:          "impressions",
	AvgCpm:               "avg_cpm",
	Clicks:               "clicks",
	Ctr:                  "ctr",
	Conversions:          "conversions",
	Cpa:                  "cpa",
	ConversionRate:       "conversion_rate",
	Actives:              "actives",
	ActiveCost:           "active_cost",
	ActiveRate:           "active_rate",
	FirstPayCount:        "first_pay_count",
	FirstPayRate:         "first_pay_rate",
}

// NewAdDesignerMaterialReportDao creates and returns a new DAO object for table data access.
func NewAdDesignerMaterialReportDao() *AdDesignerMaterialReportDao {
	return &AdDesignerMaterialReportDao{
		group:   "default",
		table:   "ad_designer_material_report",
		columns: adDesignerMaterialReportColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdDesignerMaterialReportDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdDesignerMaterialReportDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdDesignerMaterialReportDao) Columns() AdDesignerMaterialReportColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdDesignerMaterialReportDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdDesignerMaterialReportDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdDesignerMaterialReportDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
