// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/dao/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao/internal"
)

// adAccountSubjectDataStatDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adAccountSubjectDataStatDao struct {
	*internal.AdAccountSubjectDataStatDao
}

var (
	// AdAccountSubjectDataStat is globally public accessible object for table tools_gen_table operations.
	AdAccountSubjectDataStat = adAccountSubjectDataStatDao{
		internal.NewAdAccountSubjectDataStatDao(),
	}
)

// Fill with you ideas below.
