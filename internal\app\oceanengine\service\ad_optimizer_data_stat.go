// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/service/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdOptimizerDataStat interface {
	List(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (res *model.AdOptimizerDataStatSearchRes, err error)
	OptimizerSupervisorList(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (listRes *model.AdOptimizerDataStatSearchRes, err error)
	Add(ctx context.Context, req *model.AdOptimizerDataStatAddReq) (err error)
	RunAdOptimizerDataStat(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (err error)
	CalcAdOptimizerDataStatTask(ctx context.Context)
}

var localAdOptimizerDataStat IAdOptimizerDataStat

func AdOptimizerDataStat() IAdOptimizerDataStat {
	if localAdOptimizerDataStat == nil {
		panic("implement not found for interface IAdOptimizerDataStat, forgot register?")
	}
	return localAdOptimizerDataStat
}

func RegisterAdOptimizerDataStat(i IAdOptimizerDataStat) {
	localAdOptimizerDataStat = i
}
