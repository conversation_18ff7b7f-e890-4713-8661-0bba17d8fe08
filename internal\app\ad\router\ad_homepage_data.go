// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-01-24 10:42:39
// 生成路径: internal/app/ad/router/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindAdHomepageDataController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adHomepageData", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdHomepageData,
		)
	})
}
