// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/model/entity/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAdvertiserAccountHourMetricsData is the golang structure for table ad_advertiser_account_hour_metrics_data.
type AdAdvertiserAccountHourMetricsData struct {
	gmeta.Meta    `orm:"table:ad_advertiser_account_hour_metrics_data"`
	AdvertiserId  string  `orm:"advertiser_id" json:"advertiserId"`    // 账户ID
	CreateDate    string  `orm:"create_date" json:"createDate"`        // 创建日期
	Hour          int     `orm:"hour" json:"hour"`                     // 小时
	StatCost      float64 `orm:"stat_cost" json:"statCost"`            // 消耗
	StatPayAmount float64 `orm:"stat_pay_amount" json:"statPayAmount"` // 付费金额
}
