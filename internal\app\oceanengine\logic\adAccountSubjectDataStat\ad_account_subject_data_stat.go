// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/logic/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"slices"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAccountSubjectDataStat(New())
}

func New() service.IAdAccountSubjectDataStat {
	return &sAdAccountSubjectDataStat{}
}

type sAdAccountSubjectDataStat struct{}

func (s *sAdAccountSubjectDataStat) List(ctx context.Context, req *model.AdAccountSubjectDataStatSearchReq) (listRes *model.AdAccountSubjectDataStatSearchRes, err error) {
	listRes = new(model.AdAccountSubjectDataStatSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAccountSubjectDataStat.Ctx(ctx).As("a").
			LeftJoin("sys_user u", "a.user_id = u.id")
		summaryM := dao.AdAdvertiserAccount.Ctx(ctx)
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a.user_id", req.UserIds)
			summaryM = summaryM.WhereIn("user_id", req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a.user_id", userIds)
				summaryM = summaryM.WhereIn("user_id", req.UserIds)
			}
		}
		if len(req.AdvertiserCompanies) > 0 {
			m = m.WhereIn("a.advertiser_company", req.AdvertiserCompanies)
			summaryM = summaryM.WhereIn("advertiser_company", req.AdvertiserCompanies)
		}
		if req.StartTime != "" {
			m = m.WhereGTE("a.create_date", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.WhereLTE("a.create_date", req.EndTime)
			_, endTime := libUtils.GetDayStartAndEnd(req.EndTime, req.EndTime)
			summaryM = summaryM.WhereLTE("created_at", endTime)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var groupBy string
		var orderBy string
		fields := make([]string, 0)
		if req.Merge == 1 {
			groupBy = "a.advertiser_company"
			fields = append(fields, "CONCAT(MIN(a.create_date), ' - ', MAX(a.create_date)) as createDate")
		} else {
			groupBy = "a.advertiser_company, a.user_id, a.create_date"
			fields = append(fields, "ANY_VALUE(a.create_date) as createDate")
			fields = append(fields, "ANY_VALUE(a.user_id) as userId")
			fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		}
		orderBy = "statPayAmount desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields = append(fields, "ANY_VALUE(a.advertiser_company) as advertiserCompany")
		fields = append(fields, "SUM(a.total_accounts) as totalAccounts")
		fields = append(fields, "SUM(a.active_accounts) as activeAccounts")
		fields = append(fields, "ROUND(SUM(a.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost)*100,2) as payAmountRoi")
		fields = append(fields, "SUM(a.active) as active")
		fields = append(fields, "ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate")
		fields = append(fields, "SUM(a.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost)*100,2) as attributionGameInAppRoi1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost)*100,2) as attributionMicroGame0DRoi")
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
		// 计算汇总总账户数和在投总账户数
		err2 := summaryM.FieldCount("DISTINCT advertiser_id", "totalAccounts").
			FieldCount("DISTINCT CASE WHEN ad_status = 1 THEN advertiser_id END", "activeAccounts").
			Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err2, "计算总账户数和在投总账户数失败")
	})
	return
}

func (s *sAdAccountSubjectDataStat) Add(ctx context.Context, req *model.AdAccountSubjectDataStatAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAccountSubjectDataStat.Ctx(ctx).Insert(do.AdAccountSubjectDataStat{
			CreateDate:                  req.CreateDate,
			AdvertiserCompany:           req.AdvertiserCompany,
			UserId:                      req.UserId,
			TotalAccounts:               req.TotalAccounts,
			ActiveAccounts:              req.ActiveAccounts,
			StatCost:                    req.StatCost,
			StatPayAmount:               req.StatPayAmount,
			Active:                      req.Active,
			ClickCnt:                    req.ClickCnt,
			ConvertCnt:                  req.ConvertCnt,
			AttributionGameInAppLtv1Day: req.AttributionGameInAppLtv1Day,
			AttributionMicroGame0DLtv:   req.AttributionMicroGame0DLtv,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAccountSubjectDataStat) RunAdAccountSubjectDataStat(ctx context.Context, req *model.AdAccountSubjectDataStatSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		for {
			if startTime > endTime {
				break
			}
			errors := s.CalcAdAccountSubjectDataStat(innerContext, startTime)
			if errors != nil {
				g.Log().Error(ctx, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdAccountSubjectDataStat) CalcAdAccountSubjectDataStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdAccountSubjectDataStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdAccountSubjectDataStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
		err = s.CalcAdAccountSubjectDataStat(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "账户主体数据统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcAdAccountSubjectDataStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "账户主体数据统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// CalcAdAccountSubjectDataStat 账户主体数据统计
func (s *sAdAccountSubjectDataStat) CalcAdAccountSubjectDataStat(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accountSubjectList, _ := s.CalcAccountSubjectList(ctx, statDate)
		accountMetricsDataList, _ := s.CalcAccountMetricsDataList(ctx, statDate)
		var statList = make([]*model.AdAccountSubjectDataStatInfoRes, 0)
		for _, v := range accountSubjectList {
			v.CreateDate = statDate
			for _, item := range accountMetricsDataList {
				if v.AdvertiserCompany == item.AdvertiserCompany && v.UserId == item.UserId {
					v.StatCost = item.StatCost
					v.StatPayAmount = item.StatPayAmount
					v.Active = item.Active
					v.ClickCnt = item.ClickCnt
					v.ConvertCnt = item.ConvertCnt
					v.AttributionGameInAppLtv1Day = item.AttributionGameInAppLtv1Day
					v.AttributionMicroGame0DLtv = item.AttributionMicroGame0DLtv
					break
				}
			}
			statList = append(statList, v)
			if len(statList) >= 100 {
				_, err = dao.AdAccountSubjectDataStat.Ctx(ctx).Save(statList)
				statList = slices.Delete(statList, 0, len(statList))
			}
		}
		if len(statList) > 0 {
			_, err = dao.AdAccountSubjectDataStat.Ctx(ctx).Save(statList)
		}
	})
	return
}

// CalcAccountSubjectList 查询所有账户主体列表
func (s *sAdAccountSubjectDataStat) CalcAccountSubjectList(ctx context.Context, statDate string) (res []*model.AdAccountSubjectDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.AdAdvertiserAccount.Ctx(ctx).
			WhereLTE("created_at", endTime).
			Fields("advertiser_company as advertiserCompany").
			Fields("user_id as userId").
			FieldCount("DISTINCT advertiser_id", "totalAccounts").
			FieldCount("DISTINCT CASE WHEN ad_status = 1 THEN advertiser_id END", "activeAccounts").
			Group("advertiser_company, user_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询账户主体列表失败")
	})
	return
}

// CalcAccountMetricsDataList 查询账户指标统计数据
func (s *sAdAccountSubjectDataStat) CalcAccountMetricsDataList(ctx context.Context, statDate string) (res []*model.AdAccountSubjectDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccountMetricsData.Ctx(ctx).As("a").
			LeftJoin("ad_advertiser_account", "b", "a.advertiser_id = b.advertiser_id").
			Where("a.create_date", statDate).
			Fields("b.advertiser_company as advertiserCompany").
			Fields("b.user_id as userId").
			Fields("ROUND(SUM(a.stat_cost),2) as statCost").
			Fields("ROUND(SUM(a.stat_pay_amount),2) as statPayAmount").
			Fields("ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost)*100,2) as payAmountRoi").
			Fields("SUM(a.active) as active").
			Fields("SUM(a.click_cnt) as clickCnt").
			Fields("ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate").
			Fields("SUM(a.convert_cnt) as convertCnt").
			Fields("ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate").
			Fields("ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day").
			Fields("ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost)*100,2) as attributionGameInAppRoi1Day").
			Fields("ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv").
			Fields("ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost)*100,2) as attributionMicroGame0DRoi").
			Group("b.advertiser_company, b.user_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询账户指标统计数据失败")
	})
	return
}
