// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-01-24 10:42:39
// 生成路径: internal/app/ad/logic/adHomepageData/ad_homepage_data.go
// 生成人：kiro
// desc:广告首页数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonDao "github.com/tiger1103/gfast/v3/internal/app/common/dao"
	oceanDao "github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	oceanModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	systemEntity "github.com/tiger1103/gfast/v3/internal/app/system/model/entity"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
)

func init() {
	service.RegisterAdHomepageData(New())
}

func New() service.IAdHomepageData {
	return &sAdHomepageData{}
}

type sAdHomepageData struct{}

// CardData 首页卡片数据
func (s *sAdHomepageData) CardData(ctx context.Context, req *model.AdHomepageCardDataReq) (listRes *model.AdHomepageCardDataRes, err error) {
	listRes = new(model.AdHomepageCardDataRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := oceanDao.AdAdvertiserAccountMetricsData.Ctx(ctx).As("m").
			LeftJoin("ad_advertiser_account", "a", "m.advertiser_id = a.advertiser_id")
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("a.user_id", userIds)
		}
		today := gtime.Now().Format("Y-m-d")
		fields := []string{
			"SUM(m.stat_cost) as statCost",
			"SUM(m.show_cnt) as showCnt",
			"SUM(m.convert_cnt) as convertCnt",
		}
		var todayRes *oceanModel.AdAdvertiserAccountMetricsDataInfoRes
		err1 := m.Where("m.create_date", today).Fields(fields).Scan(&todayRes)
		liberr.ErrIsNil(ctx, err1)
		listRes.StatCostInfo = model.StatCostInfo{
			TodayStatCost: todayRes.StatCost,
		}
		listRes.ShowCntInfo = model.ShowCntInfo{
			TodayShowCnt: todayRes.ShowCnt,
		}
		listRes.ConvertCntInfo = model.ConvertCntInfo{
			TodayConvertCnt: todayRes.ConvertCnt,
		}
		// 查询素材数
		todayMaterialNum, totalMaterialNum, err2 := s.GetMaterialCount(ctx)
		liberr.ErrIsNil(ctx, err2, "获取素材数量失败")
		listRes.MaterialNumInfo.TodayMaterialNum = todayMaterialNum
		listRes.MaterialNumInfo.TotalMaterialNum = totalMaterialNum
	})
	return
}

// OptimizerPieChartData 优化师饼状图数据
func (s *sAdHomepageData) OptimizerPieChartData(ctx context.Context, req *model.AdHomepageOptimizerPieChartDataReq) (res *model.AdHomepageOptimizerPieChartDataRes, err error) {
	res = new(model.AdHomepageOptimizerPieChartDataRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		var data []model.PieChartData
		if req.MetricType == 1 {
			// 查询消耗数据
			data, err = s.getOptimizerCostData(ctx, userIds, admin, req.StartTime, req.EndTime)
			liberr.ErrIsNil(ctx, err, "获取优化师消耗数据失败")
		} else if req.MetricType == 2 {
			// 查询搭建广告数数据
			data, err = s.getOptimizerPromotionData(ctx, userIds, admin, req.StartTime, req.EndTime)
			liberr.ErrIsNil(ctx, err, "获取优化师搭建广告数数据失败")
		}
		res.Data = data
	})
	return
}

// DesignerPieChartData 设计师饼状图数据
func (s *sAdHomepageData) DesignerPieChartData(ctx context.Context, req *model.AdHomepageDesignerPieChartDataReq) (res *model.AdHomepageDesignerPieChartDataRes, err error) {
	res = new(model.AdHomepageDesignerPieChartDataRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		var data []model.PieChartData
		if req.MetricType == 1 {
			// 查询上新素材数数据
			data, err = s.getDesignerMaterialData(ctx, userIds, admin, req.StartTime, req.EndTime)
			liberr.ErrIsNil(ctx, err, "获取设计师上新素材数数据失败")
		} else if req.MetricType == 2 {
			// 查询消耗数据
			data, err = s.getDesignerCostData(ctx, userIds, admin, req.StartTime, req.EndTime)
			liberr.ErrIsNil(ctx, err, "获取设计师消耗数据失败")
		}
		res.Data = data
	})
	return
}

// LineChartData 折线图数据
func (s *sAdHomepageData) LineChartData(ctx context.Context, req *model.AdHomepageLineChartDataReq) (res *model.AdHomepageLineChartDataRes, err error) {
	res = new(model.AdHomepageLineChartDataRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if req.Dimension == 4 {
			res.Data, err = s.getLineChartDataByDay(ctx, userIds, admin, req)
		} else {
			res.Data, err = s.getLineChartData(ctx, userIds, admin, req)
		}
		liberr.ErrIsNil(ctx, err, "获取折线图数据失败")
	})
	return
}

// GetMaterialCount 获取用户有权限的素材数量（今日新增和总数）
func (s *sAdHomepageData) GetMaterialCount(ctx context.Context) (todayCount, totalCount int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetMaterialContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})

		today := gtime.Now().Format("Y-m-d")
		startTime, endTime := libUtils.GetDayStartAndEnd(today, today)
		if admin {
			// 管理员直接查询所有素材
			materialM := dao.AdMaterial.Ctx(ctx)
			totalCount, err = materialM.Count()
			liberr.ErrIsNil(ctx, err, "获取总素材数失败")
			todayCount, err = materialM.WhereGTE("created_at", startTime).WhereLTE("created_at", endTime).Count()
			liberr.ErrIsNil(ctx, err, "获取今日素材数失败")
			return
		}

		// 非管理员使用JOIN查询，避免大量ID的IN查询
		totalCount, err = s.getMaterialCountWithJoin(ctx, userInfo, userIds, "", "")
		liberr.ErrIsNil(ctx, err, "获取总素材数失败")
		todayCount, err = s.getMaterialCountWithJoin(ctx, userInfo, userIds, startTime, endTime)
		liberr.ErrIsNil(ctx, err, "获取今日素材数失败")
	})
	return
}

// getMaterialCountWithJoin 使用JOIN查询获取素材数量，避免大量ID的IN查询
func (s *sAdHomepageData) getMaterialCountWithJoin(ctx context.Context, userInfo *systemModel.ContextUser, userIds []int, startTime, endTime string) (count int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 构建用户ID字符串
		userIdStrs := make([]string, 0)
		userIdStrs = append(userIdStrs, "0")
		for _, id := range userIds {
			userIdStrs = append(userIdStrs, gconv.String(id))
		}
		// 构建基础SQL，使用递归CTE和JOIN避免大量ID的IN查询
		baseSql := `
			WITH RECURSIVE file_tree AS (
				-- 基础查询：获取用户有权限的专辑下的直接文件夹
				SELECT DISTINCT f.file_id, f.album_id, f.parent_id
				FROM ad_material_file f
				INNER JOIN ad_material_album a ON f.album_id = a.album_id
				LEFT JOIN ad_material_album_depts d ON a.album_id = d.album_id
				LEFT JOIN ad_material_album_users u ON a.album_id = u.album_id
				WHERE (
					a.user_id IN (` + strings.Join(userIdStrs, ",") + `) OR
					d.detp_id = ` + gconv.String(userInfo.DeptId) + ` OR
					u.specify_user_id = ` + gconv.String(userInfo.Id) + ` OR
					a.scope_authority = 4
				)
				
				UNION ALL
				
				-- 递归查询：获取子文件夹
				SELECT f.file_id, f.album_id, f.parent_id
				FROM ad_material_file f
				INNER JOIN file_tree ft ON f.parent_id = ft.file_id
			)
			SELECT COUNT(*) as count
			FROM ad_material m
			INNER JOIN file_tree ft ON m.file_id = ft.file_id
		`

		// 如果指定了时间范围，添加时间条件
		if startTime != "" && endTime != "" {
			baseSql += ` WHERE m.created_at >= '` + startTime + `' AND m.created_at <= '` + endTime + `'`
		} else if startTime != "" {
			baseSql += ` WHERE m.created_at >= '` + startTime + `'`
		} else if endTime != "" {
			baseSql += ` WHERE m.created_at <= '` + endTime + `'`
		}

		var result []struct {
			Count int `json:"count"`
		}
		err = dao.AdMaterial.DB().GetScan(ctx, &result, baseSql)
		liberr.ErrIsNil(ctx, err, "查询素材数量失败")

		if len(result) > 0 {
			count = result[0].Count
		}
	})
	return
}

// getOptimizerCostData 获取优化师消耗数据
func (s *sAdHomepageData) getOptimizerCostData(ctx context.Context, userIds []int, admin bool, startTime, endTime string) (data []model.PieChartData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := oceanDao.AdAdvertiserAccountMetricsData.Ctx(ctx).As("m").
			LeftJoin("ad_advertiser_account", "a", "m.advertiser_id = a.advertiser_id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("a.user_id", userIds)
		}
		if startTime != "" {
			m = m.WhereGTE("m.create_date", startTime)
		}
		if endTime != "" {
			m = m.WhereLTE("m.create_date", endTime)
		}
		// 按用户分组统计消耗
		fields := []string{
			"a.user_id",
			"ROUND(SUM(m.stat_cost),2) as total_cost",
		}
		var results []struct {
			UserId    uint64  `json:"user_id"`
			TotalCost float64 `json:"total_cost"`
		}
		err = m.Fields(fields).Group("a.user_id").OrderDesc("total_cost").Limit(10).Scan(&results)
		liberr.ErrIsNil(ctx, err, "查询优化师消耗数据失败")
		if len(results) == 0 {
			return
		}
		// 计算总消耗
		var totalCost float64
		for _, result := range results {
			totalCost += result.TotalCost
		}
		// 获取用户信息
		userIdList := make([]uint64, len(results))
		for i, result := range results {
			userIdList[i] = result.UserId
		}
		users, err := sysService.SysUser().GetUserByIds(ctx, userIdList)
		liberr.ErrIsNil(ctx, err, "获取用户信息失败")
		// 构建用户ID到用户名的映射
		userMap := make(map[uint64]string)
		for _, user := range users {
			userMap[user.Id] = user.UserName
		}
		// 构建返回数据
		data = make([]model.PieChartData, len(results))
		for i, result := range results {
			userName := userMap[result.UserId]
			if userName == "" {
				userName = "未知用户"
			}
			percentage := "0.00%"
			if totalCost > 0 {
				percentageValue := (result.TotalCost / totalCost) * 100
				percentage = fmt.Sprintf("%.2f%%", percentageValue)
			}
			data[i] = model.PieChartData{
				Name:       userName,
				Value:      result.TotalCost,
				Percentage: percentage,
			}
		}
	})
	return
}

// getOptimizerPromotionData 获取优化师搭建广告数数据
func (s *sAdHomepageData) getOptimizerPromotionData(ctx context.Context, userIds []int, admin bool, startTime, endTime string) (data []model.PieChartData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := oceanDao.AdPromotion.Ctx(ctx)
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("user_id", userIds)
		}
		if startTime != "" {
			m = m.WhereGTE("promotion_create_time", startTime)
		}
		if endTime != "" {
			m = m.WhereLTE("promotion_create_time", endTime)
		}
		// 按用户分组统计广告数
		fields := []string{
			"user_id",
			"COUNT(*) as promotion_count",
		}
		var results []struct {
			UserId         uint64 `json:"user_id"`
			PromotionCount int    `json:"promotion_count"`
		}
		err = m.Fields(fields).Group("user_id").OrderDesc("promotion_count").Limit(10).Scan(&results)
		liberr.ErrIsNil(ctx, err, "查询优化师搭建广告数数据失败")
		if len(results) == 0 {
			return
		}
		// 计算总广告数
		var totalCount int
		for _, result := range results {
			totalCount += result.PromotionCount
		}
		// 获取用户信息
		userIdList := make([]uint64, len(results))
		for i, result := range results {
			userIdList[i] = result.UserId
		}
		users, err := sysService.SysUser().GetUserByIds(ctx, userIdList)
		liberr.ErrIsNil(ctx, err, "获取用户信息失败")
		// 构建用户ID到用户名的映射
		userMap := make(map[uint64]string)
		for _, user := range users {
			userMap[user.Id] = user.UserName
		}
		// 构建返回数据
		data = make([]model.PieChartData, len(results))
		for i, result := range results {
			userName := userMap[result.UserId]
			if userName == "" {
				userName = "未知用户"
			}
			percentage := "0.00%"
			if totalCount > 0 {
				percentageValue := (float64(result.PromotionCount) / float64(totalCount)) * 100
				percentage = fmt.Sprintf("%.2f%%", percentageValue)
			}
			data[i] = model.PieChartData{
				Name:       userName,
				Value:      float64(result.PromotionCount),
				Percentage: percentage,
			}
		}
	})
	return
}

// getDesignerUsers 获取所有设计师角色的用户
func (s *sAdHomepageData) getDesignerUsers(ctx context.Context) (designerUsers []uint64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询设计师角色ID
		var roleList []systemEntity.SysRole
		err = systemDao.SysRole.Ctx(ctx).Where("name LIKE ?", "%设计师%").Fields("id").Scan(&roleList)
		liberr.ErrIsNil(ctx, err, "查询设计师角色失败")
		if len(roleList) == 0 {
			return
		}
		roleIds := make([]uint, 0)
		for _, role := range roleList {
			roleIds = append(roleIds, role.Id)
		}
		// 通过casbin_rule表查询拥有设计师角色的用户ID
		// casbin_rule表中ptype='g'表示用户角色关系，v0是用户ID（格式：u_用户ID），v1是角色ID
		var casbinResults []struct {
			V0 string `json:"v0"` // 用户ID，格式为u_用户ID
		}
		err = commonDao.CasbinRule.Ctx(ctx).
			Where("ptype = ?", "g").
			WhereIn("v1", roleIds).
			Fields("v0").
			Scan(&casbinResults)
		liberr.ErrIsNil(ctx, err, "查询设计师用户失败")
		// 解析用户ID，去掉u_前缀
		for _, result := range casbinResults {
			if strings.HasPrefix(result.V0, "u_") {
				userIdStr := strings.TrimPrefix(result.V0, "u_")
				if userId := gconv.Uint64(userIdStr); userId > 0 {
					designerUsers = append(designerUsers, userId)
				}
			}
		}
	})
	return
}

// getDesignerMaterialData 获取设计师上新素材数数据
func (s *sAdHomepageData) getDesignerMaterialData(ctx context.Context, userIds []int, admin bool, startTime, endTime string) (data []model.PieChartData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取所有设计师用户
		designerUsers, err := s.getDesignerUsers(ctx)
		liberr.ErrIsNil(ctx, err, "获取设计师用户失败")
		if len(designerUsers) == 0 {
			return
		}
		// 构建查询条件
		m := dao.AdMaterial.Ctx(ctx).WhereIn("design_user_id", designerUsers)
		if !admin && len(userIds) > 0 {
			m.WhereIn("design_user_id", userIds)
		}
		if startTime != "" {
			m = m.WhereGTE("created_at", startTime)
		}
		if endTime != "" {
			m = m.WhereLTE("created_at", endTime)
		}
		// 按设计师分组统计素材数
		fields := []string{
			"design_user_id",
			"COUNT(*) as material_count",
		}
		var results []struct {
			DesignUserId  uint64 `json:"design_user_id"`
			MaterialCount int    `json:"material_count"`
		}
		err = m.Fields(fields).Group("design_user_id").OrderDesc("material_count").Limit(10).Scan(&results)
		liberr.ErrIsNil(ctx, err, "查询设计师素材数据失败")
		if len(results) == 0 {
			return
		}

		// 计算总素材数
		var totalCount int
		for _, result := range results {
			totalCount += result.MaterialCount
		}
		// 获取用户信息
		userIdList := make([]uint64, len(results))
		for i, result := range results {
			userIdList[i] = result.DesignUserId
		}
		users, err := sysService.SysUser().GetUserByIds(ctx, userIdList)
		liberr.ErrIsNil(ctx, err, "获取用户信息失败")
		// 构建用户ID到用户名的映射
		userMap := make(map[uint64]string)
		for _, user := range users {
			userMap[user.Id] = user.UserName
		}
		// 构建返回数据
		data = make([]model.PieChartData, len(results))
		for i, result := range results {
			userName := userMap[result.DesignUserId]
			if userName == "" {
				userName = "未知用户"
			}
			percentage := "0.00%"
			if totalCount > 0 {
				percentageValue := (float64(result.MaterialCount) / float64(totalCount)) * 100
				percentage = fmt.Sprintf("%.2f%%", percentageValue)
			}
			data[i] = model.PieChartData{
				Name:       userName,
				Value:      float64(result.MaterialCount),
				Percentage: percentage,
			}
		}
	})
	return
}

// getDesignerCostData 获取设计师消耗数据
func (s *sAdHomepageData) getDesignerCostData(ctx context.Context, userIds []int, admin bool, startTime, endTime string) (data []model.PieChartData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取所有设计师用户
		designerUsers, err := s.getDesignerUsers(ctx)
		liberr.ErrIsNil(ctx, err, "获取设计师用户失败")
		if len(designerUsers) == 0 {
			return
		}
		// 通过素材关联查询消耗数据
		m := oceanDao.AdPromotionMetricsData.Ctx(ctx).As("pmd").
			LeftJoin("ad_material_promotion", "mp", "pmd.promotion_id = mp.promotion_id").
			LeftJoin("ad_material", "m", "mp.material_id = m.material_id")
		// 过滤设计师用户
		m = m.WhereIn("m.design_user_id", designerUsers)
		// 权限过滤
		if !admin && len(userIds) > 0 {
			m.WhereIn("m.design_user_id", userIds)
		}
		if startTime != "" {
			m = m.WhereGTE("pmd.create_date", startTime)
		}
		if endTime != "" {
			m = m.WhereLTE("pmd.create_date", endTime)
		}
		// 按设计师分组统计消耗
		fields := []string{
			"m.design_user_id",
			"ROUND(SUM(pmd.stat_cost),2) as total_cost",
		}
		var results []struct {
			DesignUserId uint64  `json:"design_user_id"`
			TotalCost    float64 `json:"total_cost"`
		}
		err = m.Fields(fields).Group("m.design_user_id").OrderDesc("total_cost").Limit(10).Scan(&results)
		liberr.ErrIsNil(ctx, err, "查询设计师消耗数据失败")
		if len(results) == 0 {
			return
		}
		// 计算总消耗
		var totalCost float64
		for _, result := range results {
			totalCost += result.TotalCost
		}
		// 获取用户信息
		userIdList := make([]uint64, len(results))
		for i, result := range results {
			userIdList[i] = result.DesignUserId
		}
		users, err := sysService.SysUser().GetUserByIds(ctx, userIdList)
		liberr.ErrIsNil(ctx, err, "获取用户信息失败")
		// 构建用户ID到用户名的映射
		userMap := make(map[uint64]string)
		for _, user := range users {
			userMap[user.Id] = user.UserName
		}
		// 构建返回数据
		data = make([]model.PieChartData, len(results))
		for i, result := range results {
			userName := userMap[result.DesignUserId]
			if userName == "" {
				userName = "未知用户"
			}
			percentage := "0.00%"
			if totalCost > 0 {
				percentageValue := (result.TotalCost / totalCost) * 100
				percentage = fmt.Sprintf("%.2f%%", percentageValue)
			}
			data[i] = model.PieChartData{
				Name:       userName,
				Value:      result.TotalCost,
				Percentage: percentage,
			}
		}
	})
	return
}

// getLineChartData 通用折线图数据查询方法
func (s *sAdHomepageData) getLineChartData(ctx context.Context, userIds []int, admin bool, req *model.AdHomepageLineChartDataReq) (data []model.LineChartData, err error) {
	data = make([]model.LineChartData, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		m := oceanDao.AdAdvertiserAccountMetricsData.Ctx(ctx).As("m").
			LeftJoin("ad_advertiser_account", "a", "m.advertiser_id = a.advertiser_id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("a.user_id", userIds)
		}
		if req.StartTime != "" {
			m = m.WhereGTE("m.create_date", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.WhereLTE("m.create_date", req.EndTime)
		}
		var fields = []string{
			"ROUND(SUM(m.stat_cost),2) as statCost",
			"ROUND(SUM(m.stat_pay_amount),2) as statPayAmount",
			"CASE WHEN SUM(m.stat_cost) > 0 THEN ROUND(SUM(m.stat_pay_amount) / SUM(m.stat_cost), 2) ELSE 0 END as payAmountRoi",
		}
		var groupBy string
		var orderBy string
		switch req.Dimension {
		case 1: // 按年 - 使用month字段
			fields = append(fields, "MONTH(m.create_date) as month")
			groupBy = "MONTH(m.create_date)"
			orderBy = "month"
		case 2, 3: // 按月/按周 - 使用date字段
			fields = append(fields, "DATE_FORMAT(m.create_date, '%Y-%m-%d') as date")
			groupBy = "DATE_FORMAT(m.create_date, '%Y-%m-%d')"
			orderBy = "date"
		default:
			return
		}
		err = m.Fields(fields).Group(groupBy).OrderAsc(orderBy).Scan(&data)
		liberr.ErrIsNil(ctx, err, "获取折线图数据失败")
	})
	return
}

// getLineChartDataByDay 按小时统计折线图数据，查询小时级别数据
func (s *sAdHomepageData) getLineChartDataByDay(ctx context.Context, userIds []int, admin bool, req *model.AdHomepageLineChartDataReq) (data []model.LineChartData, err error) {
	data = make([]model.LineChartData, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		m := oceanDao.AdAdvertiserAccountHourMetricsData.Ctx(ctx).As("h").
			LeftJoin("ad_advertiser_account", "a", "h.advertiser_id = a.advertiser_id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("a.user_id", userIds)
		}
		if req.StartTime != "" {
			m = m.WhereGTE("h.create_date", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.WhereLTE("h.create_date", req.EndTime)
		}
		// 按小时分组数据
		fields := []string{
			"h.hour as hour",
			"ROUND(SUM(h.stat_cost),2) as statCost",
			"ROUND(SUM(h.stat_pay_amount),2) as statPayAmount",
			"CASE WHEN SUM(h.stat_cost) > 0 THEN ROUND(SUM(h.stat_pay_amount) / SUM(h.stat_cost), 2) ELSE 0 END as payAmountRoi",
		}
		err = m.Fields(fields).
			Group("h.hour").
			OrderAsc("h.hour").
			Scan(&data)
		liberr.ErrIsNil(ctx, err, "获取按天统计折线图数据失败")
	})
	return
}
